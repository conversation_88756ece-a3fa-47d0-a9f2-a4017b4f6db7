'use client';

import { useState, useEffect, Suspense } from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { useSearchParams, useRouter } from 'next/navigation';
import { useAuth } from "@/components/auth/AuthContext";
import { useFavorites } from "@/contexts/FavoritesContext";
import { mockOrders, mockUserBalance, mockBankingInfo } from "@/data/mocks/account";
import { mockDealershipApplications } from "@/data/mocks/dealership";
import { Address } from "@/types";
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import EditPersonalInfoModal from "@/components/EditPersonalInfoModal";
import AddAddressModal from "@/components/AddAddressModal";
import AddressList from "@/components/AddressList";
import FavoriteModal from "@/components/FavoriteModal";
import BankingModal from "@/components/BankingModal";
import AddCardModal from "@/components/AddCardModal";
import SetDefaultCardModal from "@/components/SetDefaultCardModal";
import ReferenceRegistrationModal from "@/components/ReferenceRegistrationModal";

// 🚀 NEW: TanStack Query + Zustand imports
import { useAddresses } from "@/hooks/useAddresses";
// import { useAddressModalOpen, useAddressStore } from "@/stores/useAddressStore"; // Modal için artık kullanılmıyor
import { useProfileInfo, authKeys, useMyRefCode } from "@/hooks/useAuth";
import { useQueryClient } from "@tanstack/react-query";
import { useModalActions } from "@/stores/modalStore";
import { authService } from "@/services/authService";
import { imageService } from "@/services/imageService";
import ProfilePictureMenu from "@/components/ProfilePictureMenu";

function AccountPageContent() {
    const [activeTab, setActiveTab] = useState("profile");
    const searchParams = useSearchParams();
    const router = useRouter();

    // 💡 YENİ ve TEMİZ Auth State'i
    // Artık tek bir yerden `isLoading`, `isAuthenticated` ve `user` geliyor.
    const { user, isLoading, isAuthenticated } = useAuth();

    // 🎯 Profile Info ile detaylı profil bilgileri ve fotoğraf URL'si
    const { data: profileInfo, isLoading: isProfileLoading } = useProfileInfo();

    // 🔗 Referans kodu bilgisi
    const { data: refCodeData, isLoading: isRefCodeLoading } = useMyRefCode();

    const { favorites, removeFromFavorites } = useFavorites();

    // 🚀 ESKİ yapılar kaldırıldı.
    // const { data: userData, isLoading: userLoading, error: userError } = useUserInfo();
    // const user = userData as AuthUser | null;

    const [chartPeriod, setChartPeriod] = useState('weekly');

    // 🚀 NEW: TanStack Query hooks for server state
    const {
        data: addresses = [],
        isLoading: addressesLoading,
        error: addressesError,
        refetch: refetchAddresses
    } = useAddresses();

    // Query client for manual operations if needed
    const queryClient = useQueryClient();

    // 🚀 NEW: Profile picture update states and menu
    const [isUpdatingProfilePicture, setIsUpdatingProfilePicture] = useState(false);
    const [profilePictureError, setProfilePictureError] = useState<string | null>(null);
    const [profilePictureSuccess, setProfilePictureSuccess] = useState<string | null>(null);
    const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);

    // 🚀 OLD: Address store modal hooks (artık kullanılmıyor - modal management modalStore'da)
    // const isAddAddressModalOpen = useAddressModalOpen();
    // const openAddAddressModal = useAddressStore((state) => state.openAddAddressModal);
    // const closeAddAddressModal = useAddressStore((state) => state.closeAddAddressModal);

    // 🚀 NEW: Modal store actions
    const {
        openEditPersonalInfoModal,
        openReferenceRegistrationModal,
        openAddAddressModal: openAddressModalFromStore,
        openBankingModal,
        openAddCardModal,
        openSetDefaultCardModal
    } = useModalActions();
    const [showFavoriteModal, setShowFavoriteModal] = useState(false);
    const [favoriteModalProduct, setFavoriteModalProduct] = useState<unknown>(null);
    const [cards, setCards] = useState(mockBankingInfo.cards);
    const [copySuccess, setCopySuccess] = useState<string | null>(null);

    // Handle tab from URL only once on mount
    useEffect(() => {
        const tab = searchParams.get('tab');
        if (tab && ['profile', 'orders', 'addresses', 'favorites', 'balance', 'banking', 'settings'].includes(tab)) {
            setActiveTab(tab);
        }
    }, [searchParams]);

    // Handle auth redirect - Son ve en temiz hali
    useEffect(() => {
        // Eğer yükleme bittiyse VE kullanıcı authenticated değilse, login'e yönlendir.
        if (!isLoading && !isAuthenticated) {
            router.push('/login');
        }
    }, [isLoading, isAuthenticated, router]);

    // Refresh addresses when switching to addresses tab
    useEffect(() => {
        if (activeTab === 'addresses' && user?.id) {
            refetchAddresses();
        }
    }, [activeTab, user?.id, refetchAddresses]);

    const tabs = [
        { id: "profile", label: "Profil Bilgileri" },
        { id: "orders", label: "Sipariş Geçmişi" },
        { id: "addresses", label: "Adreslerim" },
        { id: "favorites", label: "Favorilerim" },
        { id: "balance", label: "Bakiye & Puanlar" },
        { id: "banking", label: "Banka Bilgileri" },
        { id: "settings", label: "Hesap Ayarları" },
    ];

    // 🗑️ REMOVED: Manual address loading functions - TanStack Query handles this
    // loadAddresses, handleAddressAdded, handleAddressDeleted - artık gerek yok

    // Show loading skeleton while auth is in progress
    if (isLoading || isProfileLoading || isRefCodeLoading) {
        return (
            <div className="container mx-auto px-4 py-12">
                <div className="max-w-6xl mx-auto">
                    <div className="bg-white rounded-2xl shadow-lg overflow-hidden p-8">
                        <div className="animate-pulse">
                            <div className="flex items-center mb-8 pb-6 border-b border-gray-200">
                                <div className="w-24 h-24 bg-gray-300 rounded-full mr-6"></div>
                                <div className="flex-1">
                                    <div className="h-8 bg-gray-300 rounded mb-2 w-48"></div>
                                    <div className="h-4 bg-gray-300 rounded mb-2 w-64"></div>
                                    <div className="h-4 bg-gray-300 rounded w-32"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // Auth bittikten sonra kullanıcı yoksa (yönlendirme gerçekleşene kadar) null göster
    if (!isAuthenticated || !user) {
        return null;
    }

    // Kullanıcının üyelik tarihini formatla
    const formatMemberSince = (dateString: string) => {
        const date = new Date(dateString);
        const months = [
            'Ocak', 'Şubat', 'Mart', 'Nisan', 'Mayıs', 'Haziran',
            'Temmuz', 'Ağustos', 'Eylül', 'Ekim', 'Kasım', 'Aralık'
        ];
        return `${months[date.getMonth()]} ${date.getFullYear()}`;
    };

    // Varsayılan avatar placeholder
    const getInitials = (firstName: string, lastName: string) => {
        return `${firstName.charAt(0).toUpperCase()}${lastName.charAt(0).toUpperCase()}`;
    };

    // Kart tıklama işlemi
    const handleCardClick = (card: { id: number; isDefault: boolean }) => {
        const handleSetDefaultCard = () => {
            const updatedCards = cards.map(c => ({
                ...c,
                isDefault: c.id === card.id
            }));
            setCards(updatedCards);
        };

        openSetDefaultCardModal({
            card: card,
            onConfirm: handleSetDefaultCard
        });
    };

    // Kopyalama fonksiyonu
    const copyToClipboard = async (text: string, type: string) => {
        try {
            await navigator.clipboard.writeText(text);
            setCopySuccess(type);
            setTimeout(() => setCopySuccess(null), 2000);
        } catch (err) {
            console.error('Kopyalama başarısız:', err);
        }
    };

    // Referans kayıt işlemi
    const handleReferenceRegistration = (referenceData: unknown) => {
        console.log('Referans kayıt edildi:', referenceData);
        // Burada API çağrısı yapılacak
    };

    // Gerçek referans kodu ve linkler
    const referenceCode = refCodeData?.code || 'Yükleniyor...';
    const referenceLink = refCodeData?.code ? `https://dev.sayglobalweb.com/register?refcode=${refCodeData.code}` : 'Yükleniyor...';
    const customerSalesLink = refCodeData?.code ? `https://dev.sayglobalweb.com/satis?refcode=${refCodeData.code}` : 'Yükleniyor...';

    // 🚀 NEW: Profile picture menu functions
    const handleProfilePictureMenuToggle = () => {
        setIsProfileMenuOpen(!isProfileMenuOpen);
    };

    const handleProfilePictureMenuClose = () => {
        setIsProfileMenuOpen(false);
    };

    const handleProfilePictureSelect = async (file: File) => {
        try {
            // Clear previous messages
            setProfilePictureError(null);
            setProfilePictureSuccess(null);

            // Validate file
            const validation = imageService.validateImageFile(file, 10);
            if (!validation.isValid) {
                setProfilePictureError(validation.error!);
                return;
            }

            setIsUpdatingProfilePicture(true);

            // Process image to WebP
            const result = await imageService.convertProfilePictureToWebP(file);

            // Upload to server
            await authService.updateProfilePicture(result.file);

            // Update cache
            queryClient.invalidateQueries({ queryKey: authKeys.user() });
            queryClient.invalidateQueries({ queryKey: authKeys.profileInfo() });

            // Refetch to get updated data
            await Promise.all([
                queryClient.refetchQueries({ queryKey: authKeys.user() }),
                queryClient.refetchQueries({ queryKey: authKeys.profileInfo() })
            ]);

            // Show success message
            setProfilePictureSuccess('Profil fotoğrafınız başarıyla güncellendi!');

            // Auto-clear success message after 3 seconds
            setTimeout(() => {
                setProfilePictureSuccess(null);
            }, 3000);

        } catch (error: unknown) {
            setProfilePictureError(error instanceof Error && 'response' in error && error.response && typeof error.response === 'object' && 'data' in error.response && error.response.data && typeof error.response.data === 'object' && 'message' in error.response.data ? String(error.response.data.message) : 'Profil fotoğrafı güncellenirken bir hata oluştu');

            // Auto-clear error after 5 seconds
            setTimeout(() => {
                setProfilePictureError(null);
            }, 5000);
        } finally {
            setIsUpdatingProfilePicture(false);
        }
    };

    const handleProfilePictureDeleteSuccess = () => {
        setProfilePictureSuccess('Profil fotoğrafınız başarıyla kaldırıldı!');

        // Auto-clear success message after 3 seconds
        setTimeout(() => {
            setProfilePictureSuccess(null);
        }, 3000);
    };

    const handleProfilePictureDeleteError = (error: string) => {
        setProfilePictureError(error);

        // Auto-clear error after 5 seconds
        setTimeout(() => {
            setProfilePictureError(null);
        }, 5000);
    };

    return (
        <div className="container mx-auto px-4 py-12">
            <div className="max-w-6xl mx-auto">
                <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
                    <div className="p-6 md:p-8">
                        {/* Profile Picture Messages */}
                        {profilePictureError && (
                            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                                <div className="flex items-center">
                                    <svg className="h-5 w-5 text-red-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span className="text-red-700">{profilePictureError}</span>
                                </div>
                            </div>
                        )}

                        {profilePictureSuccess && (
                            <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                                <div className="flex items-center">
                                    <svg className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span className="text-green-700">{profilePictureSuccess}</span>
                                </div>
                            </div>
                        )}

                        {/* Hesap Başlığı */}
                        <div className="flex flex-col md:flex-row items-center mb-8 pb-6 border-b border-gray-200">
                            <div className="relative mb-4 md:mb-0 md:mr-6">
                                <div className="w-24 h-24 rounded-full bg-gradient-to-br from-purple-600 to-indigo-600 flex items-center justify-center overflow-hidden">
                                    {profileInfo?.profilePictureUrl ? (
                                        <img
                                            src={profileInfo.profilePictureUrl}
                                            alt="Profile"
                                            className="w-full h-full object-cover"
                                            onError={(e) => {
                                                // Fotoğraf yüklenemezse initials göster
                                                (e.currentTarget as HTMLImageElement).style.display = 'none';
                                                (e.currentTarget.nextElementSibling as HTMLElement).style.display = 'flex';
                                            }}
                                        />
                                    ) : null}
                                    <span
                                        className={`text-white text-xl font-bold ${profileInfo?.profilePictureUrl ? 'hidden' : 'flex'} items-center justify-center w-full h-full`}
                                    >
                                        {getInitials(user.firstName, user.lastName)}
                                    </span>
                                </div>
                                <motion.button
                                    className={`absolute bottom-0 right-0 rounded-full p-1.5 shadow-md transition-all ${isUpdatingProfilePicture
                                        ? 'bg-purple-600 cursor-not-allowed'
                                        : 'bg-white border border-gray-200 hover:bg-purple-50'
                                        }`}
                                    whileHover={!isUpdatingProfilePicture ? { scale: 1.1 } : {}}
                                    whileTap={!isUpdatingProfilePicture ? { scale: 0.9 } : {}}
                                    onClick={handleProfilePictureMenuToggle}
                                    disabled={isUpdatingProfilePicture}
                                >
                                    {isUpdatingProfilePicture ? (
                                        <svg className="h-4 w-4 text-white animate-spin" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                    ) : (
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                        </svg>
                                    )}
                                </motion.button>

                                <ProfilePictureMenu
                                    isOpen={isProfileMenuOpen}
                                    onClose={handleProfilePictureMenuClose}
                                    onFileSelect={handleProfilePictureSelect}
                                    hasProfilePicture={!!profileInfo?.profilePictureUrl}
                                    onDeleteSuccess={handleProfilePictureDeleteSuccess}
                                    onDeleteError={handleProfilePictureDeleteError}
                                    isDeleting={isUpdatingProfilePicture}
                                    mode="immediate"
                                />
                            </div>
                            <div className="text-center md:text-left">
                                <h1 className="text-2xl md:text-3xl font-bold text-gray-800 mb-1">
                                    {user.firstName} {user.lastName}
                                </h1>
                                <p className="text-gray-700 mb-2">{user.email}</p>
                                <div className="flex items-center justify-center md:justify-start space-x-4">
                                    <p className="text-sm text-gray-600">
                                        Üyelik başlangıcı: {formatMemberSince(user.joinDate)}
                                    </p>
                                    <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${user.role === 'admin' ? 'bg-red-100 text-red-800' :
                                        user.role === 'dealership' ? 'bg-green-100 text-green-800' :
                                            'bg-blue-100 text-blue-800'
                                        }`}>
                                        {user.role === 'admin' ? 'Yönetici' :
                                            user.role === 'dealership' ? 'Satıcı' : 'Müşteri'}
                                    </span>
                                </div>
                            </div>
                            <div className="flex items-center md:ml-auto mt-4 md:mt-0">
                                <Link href="/account/edit">
                                    <motion.button
                                        className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-4 py-2 rounded-lg font-medium hover:shadow-lg transition duration-300 flex items-center space-x-1"
                                        whileHover={{ scale: 1.05 }}
                                        whileTap={{ scale: 0.95 }}
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                        </svg>
                                        <span>Profili Düzenle</span>
                                    </motion.button>
                                </Link>
                            </div>
                        </div>

                        {/* Sekmeler */}
                        <div className="mb-8">
                            <div className="flex flex-col sm:flex-row items-center overflow-x-auto pb-2">
                                {tabs.map((tab) => (
                                    <motion.button
                                        key={tab.id}
                                        className={`px-5 py-2.5 rounded-lg font-medium text-sm whitespace-nowrap mb-2 sm:mb-0 sm:mr-2 ${activeTab === tab.id
                                            ? "bg-gradient-to-r from-purple-600 to-indigo-600 text-white shadow-md"
                                            : "text-gray-600 hover:bg-gray-100"
                                            }`}
                                        onClick={() => {
                                            setActiveTab(tab.id);
                                            window.history.pushState({}, '', `/account?tab=${tab.id}`);
                                        }}
                                        whileHover={activeTab !== tab.id ? { scale: 1.05 } : {}}
                                        whileTap={activeTab !== tab.id ? { scale: 0.95 } : {}}
                                    >
                                        {tab.label}
                                    </motion.button>
                                ))}
                            </div>
                        </div>

                        {/* Sekme İçeriği */}
                        <div className="min-h-[400px]">
                            {activeTab === "profile" && (
                                <motion.div
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.3 }}
                                >
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                        <div>
                                            <h2 className="text-xl font-semibold text-gray-800 mb-4">Kişisel Bilgiler</h2>
                                            <div className="bg-gray-50 rounded-lg p-6">
                                                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8">
                                                    {/* Sol Sütun */}
                                                    <div className="space-y-6">
                                                        <div>
                                                            <p className="text-sm text-black mb-1">Ad Soyad</p>
                                                            <p className="font-medium text-gray-600">
                                                                {profileInfo?.firstName || user.firstName} {profileInfo?.lastName || user.lastName}
                                                            </p>
                                                        </div>
                                                        <div>
                                                            <p className="text-sm text-black mb-1">E-posta</p>
                                                            <p className="font-medium text-gray-600">{profileInfo?.email || user.email}</p>
                                                        </div>
                                                        <div>
                                                            <p className="text-sm text-black mb-1">Telefon</p>
                                                            <p className="font-medium text-gray-600">
                                                                {user.phoneNumber || 'Telefon bilgisi eklenmemiş'}
                                                            </p>
                                                        </div>
                                                        <div>
                                                            <p className="text-sm text-black mb-1">Hesap Türü</p>
                                                            <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${user.role === 'admin' ? 'bg-red-100 text-red-800' :
                                                                user.role === 'dealership' ? 'bg-green-100 text-green-800' :
                                                                    user.membershipLevel ? 'bg-purple-100 text-purple-800' :
                                                                        'bg-blue-100 text-blue-800'
                                                                }`}>
                                                                {user.role === 'admin' ? 'Yönetici' :
                                                                    user.role === 'dealership' ? 'Satıcı' :
                                                                        user.membershipLevel > 0 ? 'Distribütör' : 'Müşteri'}
                                                            </span>
                                                        </div>
                                                    </div>

                                                    {/* Sağ Sütun */}
                                                    <div className="space-y-6 mt-6 md:mt-0">
                                                        <div>
                                                            <p className="text-sm text-black mb-1">Doğum Tarihi</p>
                                                            <p className="font-medium text-gray-600">
                                                                {profileInfo?.dateOfBirth ? new Date(profileInfo.dateOfBirth).toLocaleDateString('tr-TR') : 'Belirtilmemiş'}
                                                            </p>
                                                        </div>
                                                        <div>
                                                            <p className="text-sm text-black mb-1">Cinsiyet</p>
                                                            <p className="font-medium text-gray-600">
                                                                {(profileInfo?.gender !== undefined && profileInfo.gender !== null && profileInfo.gender > 0) ?
                                                                    (profileInfo.gender === 1 ? 'Erkek' :
                                                                        profileInfo.gender === 2 ? 'Kadın' :
                                                                            profileInfo.gender === 3 ? 'Diğer' : 'Belirtilmemiş') :
                                                                    'Belirtilmemiş'
                                                                }
                                                            </p>
                                                        </div>
                                                        <div>
                                                            <p className="text-sm text-black mb-1">Konum</p>
                                                            <p className="font-medium text-gray-600">
                                                                {profileInfo?.location || 'Belirtilmemiş'}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>

                                                {/* Güncelleme Butonu */}
                                                <div className="mt-6 border-t border-gray-200 pt-5">
                                                    <motion.button
                                                        className="text-purple-600 text-sm font-medium flex items-center"
                                                        onClick={() => openEditPersonalInfoModal({
                                                            firstName: profileInfo?.firstName || user?.firstName || '',
                                                            lastName: profileInfo?.lastName || user?.lastName || '',
                                                            phoneNumber: user?.phoneNumber || '',
                                                        })}
                                                        whileHover={{ x: 2 }}
                                                    >
                                                        Kişisel bilgilerimi güncelle
                                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                                        </svg>
                                                    </motion.button>
                                                </div>
                                            </div>
                                        </div>

                                        <div>
                                            <h2 className="text-xl font-semibold text-gray-800 mb-4">Linkler</h2>
                                            <div className="bg-gray-50 rounded-lg p-5 space-y-4">
                                                {/* Referans Linki */}
                                                <div>
                                                    <p className="text-sm text-black mb-2">Referans Linki</p>
                                                    <div className="flex items-center space-x-2">
                                                        <input
                                                            type="text"
                                                            value={referenceLink}
                                                            readOnly
                                                            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-600 text-sm"
                                                        />
                                                        <motion.button
                                                            onClick={() => copyToClipboard(referenceLink, 'reference')}
                                                            disabled={isRefCodeLoading || !refCodeData?.code}
                                                            className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${isRefCodeLoading || !refCodeData?.code
                                                                ? 'bg-gray-400 text-white cursor-not-allowed'
                                                                : copySuccess === 'reference'
                                                                    ? 'bg-green-500 text-white'
                                                                    : 'bg-purple-600 text-white hover:bg-purple-700'
                                                                }`}
                                                            whileHover={!isRefCodeLoading && refCodeData?.code ? { scale: 1.02 } : {}}
                                                            whileTap={!isRefCodeLoading && refCodeData?.code ? { scale: 0.98 } : {}}
                                                        >
                                                            {isRefCodeLoading ? 'Yükleniyor...' : copySuccess === 'reference' ? 'Kopyalandı!' : 'Kopyala'}
                                                        </motion.button>
                                                    </div>
                                                </div>

                                                {/* Müşteri Satış Linki */}
                                                <div>
                                                    <p className="text-sm text-black mb-2">Müşteri Satış Linki</p>
                                                    <div className="flex items-center space-x-2">
                                                        <input
                                                            type="text"
                                                            value={customerSalesLink}
                                                            readOnly
                                                            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-600 text-sm"
                                                        />
                                                        <motion.button
                                                            onClick={() => copyToClipboard(customerSalesLink, 'sales')}
                                                            disabled={isRefCodeLoading || !refCodeData?.code}
                                                            className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${isRefCodeLoading || !refCodeData?.code
                                                                ? 'bg-gray-400 text-white cursor-not-allowed'
                                                                : copySuccess === 'sales'
                                                                    ? 'bg-green-500 text-white'
                                                                    : 'bg-purple-600 text-white hover:bg-purple-700'
                                                                }`}
                                                            whileHover={!isRefCodeLoading && refCodeData?.code ? { scale: 1.02 } : {}}
                                                            whileTap={!isRefCodeLoading && refCodeData?.code ? { scale: 0.98 } : {}}
                                                        >
                                                            {isRefCodeLoading ? 'Yükleniyor...' : copySuccess === 'sales' ? 'Kopyalandı!' : 'Kopyala'}
                                                        </motion.button>
                                                    </div>
                                                </div>

                                                {/* Referans Kodum */}
                                                <div>
                                                    <p className="text-sm text-black mb-2">Referans Kodum</p>
                                                    <div className="flex items-center space-x-2">
                                                        <input
                                                            type="text"
                                                            value={referenceCode}
                                                            readOnly
                                                            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-600 text-sm font-mono font-semibold tracking-wider"
                                                        />
                                                        <motion.button
                                                            onClick={() => copyToClipboard(referenceCode, 'code')}
                                                            disabled={isRefCodeLoading || !refCodeData?.code}
                                                            className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${isRefCodeLoading || !refCodeData?.code
                                                                    ? 'bg-gray-400 text-white cursor-not-allowed'
                                                                    : copySuccess === 'code'
                                                                        ? 'bg-green-500 text-white'
                                                                        : 'bg-purple-600 text-white hover:bg-purple-700'
                                                                }`}
                                                            whileHover={!isRefCodeLoading && refCodeData?.code ? { scale: 1.02 } : {}}
                                                            whileTap={!isRefCodeLoading && refCodeData?.code ? { scale: 0.98 } : {}}
                                                        >
                                                            {isRefCodeLoading ? 'Yükleniyor...' : copySuccess === 'code' ? 'Kopyalandı!' : 'Kopyala'}
                                                        </motion.button>
                                                    </div>
                                                </div>

                                                {/* Referans Kayıt Et Butonu */}
                                                <div className="pt-2">
                                                    <motion.button
                                                        className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-4 py-3 rounded-lg font-medium hover:shadow-lg transition duration-300 flex items-center justify-center space-x-2"
                                                        onClick={() => openReferenceRegistrationModal()}
                                                        whileHover={{ scale: 1.02 }}
                                                        whileTap={{ scale: 0.98 }}
                                                    >
                                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                                                        </svg>
                                                        <span>Referans Kayıt Et</span>
                                                    </motion.button>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Satıcı Başvuru Durumu */}
                                        <div className="md:col-span-2 mt-8">
                                            <h2 className="text-xl font-semibold text-gray-800 mb-4">Satıcı Başvuru Durumu</h2>
                                            <div className="bg-gray-50 rounded-lg p-5">
                                                {(() => {
                                                    // Kullanıcının başvuru durumunu kontrol et
                                                    const userApplication = mockDealershipApplications.find(app => app.userId === user.id);

                                                    if (user.role === 'dealership') {
                                                        return (
                                                            <div className="text-center py-6">
                                                                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                                    </svg>
                                                                </div>
                                                                <h3 className="text-lg font-semibold text-green-800 mb-2">Satıcı Onaylandı</h3>
                                                                <p className="text-green-600">Tebrikler! Artık Say Global'de satıcı olarak ürün satabilirsiniz.</p>
                                                                <Link href="/panel" className="inline-block mt-4 bg-green-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-green-700 transition duration-300">
                                                                    Satıcı Paneline Git
                                                                </Link>
                                                            </div>
                                                        );
                                                    } else if (userApplication) {
                                                        return (
                                                            <div className="text-center py-6">
                                                                {userApplication.status === 'pending' && (
                                                                    <>
                                                                        <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                                            </svg>
                                                                        </div>
                                                                        <h3 className="text-lg font-semibold text-yellow-800 mb-2">Başvuru İnceleniyor</h3>
                                                                        <p className="text-yellow-600 mb-2">Satıcı başvurunuz admin ekibimiz tarafından inceleniyor.</p>
                                                                        <p className="text-sm text-gray-600">Başvuru Tarihi: {new Date(userApplication.submittedAt).toLocaleDateString('tr-TR')}</p>
                                                                    </>
                                                                )}
                                                                {userApplication.status === 'rejected' && (
                                                                    <>
                                                                        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                                                            </svg>
                                                                        </div>
                                                                        <h3 className="text-lg font-semibold text-red-800 mb-2">Başvuru Reddedildi</h3>
                                                                        <p className="text-red-600 mb-2">Maalesef satıcı başvurunuz reddedildi.</p>
                                                                        {userApplication.adminNotes && (
                                                                            <p className="text-sm text-gray-600 mb-4">Admin Notu: {userApplication.adminNotes}</p>
                                                                        )}
                                                                        <Link href="/become-dealer" className="inline-block bg-purple-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-purple-700 transition duration-300">
                                                                            Yeni Başvuru Yap
                                                                        </Link>
                                                                    </>
                                                                )}
                                                            </div>
                                                        );
                                                    } else {
                                                        return (
                                                            <div className="text-center py-6">
                                                                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                                                                    </svg>
                                                                </div>
                                                                <h3 className="text-lg font-semibold text-gray-800 mb-2">Satıcı Başvurusu Yap</h3>
                                                                <p className="text-gray-600 mb-4">Say Global'de satıcı olarak ürün satmaya başlayın.</p>
                                                                <Link href="/become-dealer" className="inline-block bg-purple-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-purple-700 transition duration-300">
                                                                    Satıcı Başvurusu Yap
                                                                </Link>
                                                            </div>
                                                        );
                                                    }
                                                })()}
                                            </div>
                                        </div>
                                    </div>
                                </motion.div>
                            )}

                            {activeTab === "orders" && (
                                <motion.div
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.3 }}
                                >
                                    <h2 className="text-xl font-semibold text-gray-800 mb-4">Sipariş Geçmişiniz</h2>
                                    {mockOrders.length > 0 ? (
                                        <div className="overflow-x-auto">
                                            <table className="min-w-full divide-y divide-gray-200">
                                                <thead className="bg-gray-50">
                                                    <tr>
                                                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                            Sipariş No
                                                        </th>
                                                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                            Tarih
                                                        </th>
                                                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                            Durum
                                                        </th>
                                                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                            Toplam
                                                        </th>
                                                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                            Ürünler
                                                        </th>
                                                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                            İşlemler
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody className="bg-white divide-y divide-gray-200">
                                                    {mockOrders.map((order) => (
                                                        <tr key={order.id} className="hover:bg-gray-50">
                                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                                {order.id}
                                                            </td>
                                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                                {order.date}
                                                            </td>
                                                            <td className="px-6 py-4 whitespace-nowrap">
                                                                <span className={`px-2.5 py-1 rounded-full text-xs font-medium ${order.statusClass}`}>
                                                                    {order.status}
                                                                </span>
                                                            </td>
                                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                                                                {order.total}
                                                            </td>
                                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                                {order.items} ürün
                                                            </td>
                                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                                <Link href={`/order-details/${order.id}`}>
                                                                    <motion.span
                                                                        className="text-purple-600 hover:text-purple-800 font-medium text-sm cursor-pointer"
                                                                        whileHover={{ x: 2 }}
                                                                    >
                                                                        Detaylar
                                                                    </motion.span>
                                                                </Link>
                                                            </td>
                                                        </tr>
                                                    ))}
                                                </tbody>
                                            </table>
                                        </div>
                                    ) : (
                                        <div className="text-center py-10 bg-gray-50 rounded-lg">
                                            <div className="mb-4">
                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                                                </svg>
                                            </div>
                                            <h3 className="text-lg font-medium text-gray-900 mb-2">Henüz siparişiniz bulunmuyor</h3>
                                            <p className="text-gray-500 mb-6">Ürünlerimizi keşfedin ve ilk siparişinizi oluşturun.</p>
                                            <Link href="/products" className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-2.5 rounded-lg font-medium hover:shadow-lg transition duration-300">
                                                Alışverişe Başla
                                            </Link>
                                        </div>
                                    )}
                                </motion.div>
                            )}

                            {activeTab === "addresses" && (
                                <motion.div
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.3 }}
                                    className="space-y-8"
                                >
                                    {/* 📋 Address List */}
                                    <div>
                                        <div className="flex justify-between items-center mb-6">
                                            <h2 className="text-xl font-semibold text-gray-800">Kayıtlı Adresleriniz</h2>
                                            <motion.button
                                                className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-4 py-2 rounded-lg font-medium hover:shadow-lg transition duration-300 flex items-center space-x-1"
                                                onClick={() => openAddressModalFromStore()}
                                                whileHover={{ scale: 1.05 }}
                                                whileTap={{ scale: 0.95 }}
                                                disabled={addressesLoading}
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                                                </svg>
                                                <span>Yeni Adres Ekle</span>
                                            </motion.button>
                                        </div>

                                        {addressesError && (
                                            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                                                {addressesError.message || 'Adresler yüklenirken bir hata oluştu'}
                                            </div>
                                        )}

                                        {addressesLoading ? (
                                            <div className="grid gap-4 md:grid-cols-2">
                                                {/* Skeleton Loading */}
                                                {[...Array(2)].map((_, index) => (
                                                    <div key={index} className="bg-white border border-gray-200 rounded-lg p-4 animate-pulse">
                                                        <div className="flex justify-between items-start mb-3">
                                                            <div className="flex items-center space-x-2">
                                                                <div className="h-6 bg-gray-300 rounded w-24"></div>
                                                                <div className="h-5 bg-gray-200 rounded w-16"></div>
                                                            </div>
                                                            <div className="h-8 w-8 bg-gray-200 rounded-lg"></div>
                                                        </div>
                                                        <div className="space-y-2">
                                                            <div className="flex items-start">
                                                                <div className="w-4 h-4 bg-gray-200 rounded mt-0.5 mr-2"></div>
                                                                <div className="h-4 bg-gray-300 rounded flex-1"></div>
                                                            </div>
                                                            <div className="flex items-center">
                                                                <div className="w-4 h-4 bg-gray-200 rounded mr-2"></div>
                                                                <div className="h-4 bg-gray-300 rounded w-32"></div>
                                                            </div>
                                                            <div className="flex items-center">
                                                                <div className="w-4 h-4 bg-gray-200 rounded mr-2"></div>
                                                                <div className="h-4 bg-gray-300 rounded w-24"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        ) : addresses && addresses.length >= 0 ? (
                                            <AddressList
                                                addresses={addresses as Address[]}
                                                onAddressDeleted={() => { }}
                                            />
                                        ) : null}
                                    </div>
                                </motion.div>
                            )}

                            {activeTab === "favorites" && (
                                <motion.div
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.3 }}
                                >
                                    <h2 className="text-xl font-semibold text-gray-800 mb-6">Favori Ürünleriniz</h2>

                                    {favorites.length === 0 ? (
                                        <div className="text-center py-10 bg-gray-50 rounded-lg">
                                            <div className="mb-4">
                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                                </svg>
                                            </div>
                                            <h3 className="text-lg font-medium text-gray-900 mb-2">Favori listeniz boş</h3>
                                            <p className="text-gray-500 mb-6">Beğendiğiniz ürünleri favorilerinize ekleyerek daha sonra kolayca bulabilirsiniz.</p>
                                            <Link href="/products" className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-2.5 rounded-lg font-medium hover:shadow-lg transition duration-300">
                                                Ürünlere Göz At
                                            </Link>
                                        </div>
                                    ) : (
                                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                                            {favorites.map((favorite) => (
                                                <motion.div
                                                    key={favorite.id}
                                                    initial={{ opacity: 0, y: 20 }}
                                                    animate={{ opacity: 1, y: 0 }}
                                                    transition={{ duration: 0.3 }}
                                                    whileHover={{ y: -5 }}
                                                    className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300"
                                                >
                                                    <div className="relative group">
                                                        <Link href={`/product/${favorite.product.id}`}>
                                                            <div className="relative h-48 overflow-hidden">
                                                                <img
                                                                    src={favorite.product.thumbnail}
                                                                    alt={favorite.product.title}
                                                                    className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                                                                />
                                                                {favorite.product.discountPercentage && favorite.product.discountPercentage > 0 && (
                                                                    <div className="absolute top-3 right-3 bg-red-500 text-white px-2 py-1 rounded-full text-sm font-medium">
                                                                        %{favorite.product.discountPercentage} İndirim
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </Link>

                                                        {/* Remove from favorites button */}
                                                        <motion.button
                                                            onClick={() => {
                                                                removeFromFavorites(favorite.product.id);
                                                                setFavoriteModalProduct(favorite.product);
                                                                setShowFavoriteModal(true);
                                                            }}
                                                            className="absolute top-3 left-3 p-2 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:bg-white transition-all duration-300"
                                                            whileHover={{ scale: 1.1 }}
                                                            whileTap={{ scale: 0.9 }}
                                                        >
                                                            <svg
                                                                className="h-5 w-5 text-red-500 fill-current"
                                                                fill="currentColor"
                                                                stroke="currentColor"
                                                                viewBox="0 0 24 24"
                                                            >
                                                                <path
                                                                    strokeLinecap="round"
                                                                    strokeLinejoin="round"
                                                                    strokeWidth={2}
                                                                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                                                                />
                                                            </svg>
                                                        </motion.button>
                                                    </div>

                                                    <div className="p-4">
                                                        <Link href={`/product/${favorite.product.id}`}>
                                                            <h3 className="text-lg font-semibold mb-1 text-gray-800 hover:text-purple-600 transition-colors">
                                                                {favorite.product.title}
                                                            </h3>
                                                        </Link>
                                                        <p className="text-gray-600 mb-2 text-sm">{favorite.product.brand}</p>
                                                        <p className="text-gray-500 text-sm mb-3 line-clamp-2">{favorite.product.description}</p>

                                                        <div className="flex justify-between items-center mb-3">
                                                            <div>
                                                                <span className="text-lg font-bold text-purple-700">
                                                                    {favorite.product.discountPercentage ? (
                                                                        (favorite.product.price * (1 - favorite.product.discountPercentage / 100)).toFixed(2)
                                                                    ) : (
                                                                        favorite.product.price.toFixed(2)
                                                                    )} ₺
                                                                </span>
                                                                {favorite.product.discountPercentage && favorite.product.discountPercentage > 0 && (
                                                                    <span className="text-sm text-gray-500 line-through ml-2">
                                                                        {favorite.product.price.toFixed(2)} ₺
                                                                    </span>
                                                                )}
                                                            </div>
                                                            <div className="flex items-center bg-yellow-50 px-2 py-1 rounded">
                                                                <svg
                                                                    xmlns="http://www.w3.org/2000/svg"
                                                                    className="h-4 w-4 text-yellow-400"
                                                                    viewBox="0 0 20 20"
                                                                    fill="currentColor"
                                                                >
                                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                                </svg>
                                                                <span className="ml-1 text-xs font-medium text-yellow-700">{favorite.product.rating}</span>
                                                            </div>
                                                        </div>

                                                        <div className="text-xs text-gray-500 mb-3">
                                                            Favorilere eklendi: {new Date(favorite.addedAt).toLocaleDateString('tr-TR')}
                                                        </div>

                                                        <Link href={`/product/${favorite.product.id}`}>
                                                            <motion.button
                                                                whileHover={{ scale: 1.02 }}
                                                                whileTap={{ scale: 0.98 }}
                                                                className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-2 px-4 rounded-lg font-medium hover:from-purple-700 hover:to-indigo-700 transition-all duration-300"
                                                            >
                                                                Ürünü İncele
                                                            </motion.button>
                                                        </Link>
                                                    </div>
                                                </motion.div>
                                            ))}
                                        </div>
                                    )}
                                </motion.div>
                            )}

                            {activeTab === "balance" && (
                                <motion.div
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.3 }}
                                >
                                    <h2 className="text-xl font-semibold text-gray-800 mb-6">Bakiye & Puanlar</h2>

                                    {/* Bakiye ve Puan Kartları */}
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                                        {/* Hesap Bakiyesi Kartı */}
                                        <div className="bg-gradient-to-br from-green-50 to-emerald-100 rounded-xl p-6 border border-green-200">
                                            <div className="flex items-center justify-between mb-4">
                                                <div className="flex items-center">
                                                    <div className="bg-green-500 p-2 rounded-full mr-3">
                                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                                        </svg>
                                                    </div>
                                                    <h3 className="text-lg font-semibold text-gray-800">Hesap Bakiyesi</h3>
                                                </div>
                                            </div>
                                            <div className="text-3xl font-bold text-green-600 mb-2">
                                                ₺{mockUserBalance.accountBalance.toFixed(2)}
                                            </div>
                                            <p className="text-green-700 text-sm">
                                                Kullanılabilir bakiye
                                            </p>
                                        </div>

                                        {/* Puanlar Kartı */}
                                        <div className="bg-gradient-to-br from-purple-50 to-indigo-100 rounded-xl p-6 border border-purple-200">
                                            <div className="flex items-center justify-between mb-4">
                                                <div className="flex items-center">
                                                    <div className="bg-purple-500 p-2 rounded-full mr-3">
                                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                                                        </svg>
                                                    </div>
                                                    <h3 className="text-lg font-semibold text-gray-800">Toplam Puanlar</h3>
                                                </div>
                                            </div>
                                            <div className="text-3xl font-bold text-purple-600 mb-2">
                                                {mockUserBalance.totalPoints.toLocaleString()}
                                            </div>
                                            <div className="flex justify-between text-sm">
                                                <span className="text-purple-700">
                                                    Kullanılabilir: {mockUserBalance.availablePoints.toLocaleString()}
                                                </span>
                                                <span className="text-purple-500">
                                                    Kullanılan: {mockUserBalance.usedPoints.toLocaleString()}
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Puan Kazanım Grafikleri */}
                                    <div className="mb-8">
                                        <h3 className="text-lg font-semibold text-gray-800 mb-6">Puan Kazanım Analizi</h3>

                                        {/* Chart Toggle */}
                                        <div className="flex mb-6">
                                            <div className="bg-gray-100 rounded-lg p-1 flex">
                                                <button
                                                    className={`px-4 py-2 rounded-md font-medium text-sm transition-all ${chartPeriod === 'weekly'
                                                        ? 'bg-white text-purple-600 shadow-sm'
                                                        : 'text-gray-600 hover:text-gray-800'
                                                        }`}
                                                    onClick={() => setChartPeriod('weekly')}
                                                >
                                                    Haftalık
                                                </button>
                                                <button
                                                    className={`px-4 py-2 rounded-md font-medium text-sm transition-all ${chartPeriod === 'monthly'
                                                        ? 'bg-white text-purple-600 shadow-sm'
                                                        : 'text-gray-600 hover:text-gray-800'
                                                        }`}
                                                    onClick={() => setChartPeriod('monthly')}
                                                >
                                                    Aylık
                                                </button>
                                            </div>
                                        </div>

                                        {/* Chart Container */}
                                        <div className="bg-white rounded-xl border border-gray-200 p-6">
                                            <div className="h-80">
                                                <ResponsiveContainer width="100%" height="100%">
                                                    <AreaChart
                                                        data={chartPeriod === 'weekly' ? mockUserBalance.weeklyEarnings : mockUserBalance.monthlyEarnings}
                                                        margin={{
                                                            top: 10,
                                                            right: 30,
                                                            left: 0,
                                                            bottom: 0,
                                                        }}
                                                    >
                                                        <defs>
                                                            <linearGradient id="purpleGradient" x1="0" y1="0" x2="0" y2="1">
                                                                <stop offset="5%" stopColor="#8b5cf6" stopOpacity={0.8} />
                                                                <stop offset="95%" stopColor="#8b5cf6" stopOpacity={0.1} />
                                                            </linearGradient>
                                                        </defs>
                                                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                                                        <XAxis
                                                            dataKey={chartPeriod === 'weekly' ? 'week' : 'month'}
                                                            axisLine={false}
                                                            tickLine={false}
                                                            tick={{ fontSize: 12, fill: '#6b7280' }}
                                                        />
                                                        <YAxis
                                                            axisLine={false}
                                                            tickLine={false}
                                                            tick={{ fontSize: 12, fill: '#6b7280' }}
                                                        />
                                                        <Tooltip
                                                            contentStyle={{
                                                                backgroundColor: 'white',
                                                                border: '1px solid #e5e7eb',
                                                                borderRadius: '8px',
                                                                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                                                            }}
                                                            labelStyle={{ color: '#374151', fontWeight: 'medium' }}
                                                            formatter={(value: any) => [`${value} puan`, 'Kazanılan Puan']}
                                                        />
                                                        <Area
                                                            type="monotone"
                                                            dataKey="points"
                                                            stroke="#8b5cf6"
                                                            fillOpacity={1}
                                                            fill="url(#purpleGradient)"
                                                            strokeWidth={2}
                                                        />
                                                    </AreaChart>
                                                </ResponsiveContainer>
                                            </div>
                                            <div className="flex justify-between items-center mt-4 pt-4 border-t border-gray-100">
                                                <div className="text-sm text-gray-600">
                                                    {chartPeriod === 'weekly' ? 'Son 8 hafta' : 'Son 6 ay'} içerisinde kazandığınız puanlar
                                                </div>
                                                <div className="text-sm font-medium text-purple-600">
                                                    Toplam: {(chartPeriod === 'weekly'
                                                        ? mockUserBalance.weeklyEarnings.reduce((sum, item) => sum + item.points, 0)
                                                        : mockUserBalance.monthlyEarnings.reduce((sum, item) => sum + item.points, 0)
                                                    ).toLocaleString()} puan
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Puan Geçmişi */}
                                    <div className="mb-8">
                                        <h3 className="text-lg font-semibold text-gray-800 mb-4">Puan Geçmişi</h3>
                                        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                                            <div className="overflow-x-auto">
                                                <table className="min-w-full divide-y divide-gray-200">
                                                    <thead className="bg-gray-50">
                                                        <tr>
                                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                                Tarih
                                                            </th>
                                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                                Açıklama
                                                            </th>
                                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                                Sipariş No
                                                            </th>
                                                            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                                Puan
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody className="bg-white divide-y divide-gray-200">
                                                        {mockUserBalance.pointHistory.map((history) => (
                                                            <tr key={history.id} className="hover:bg-gray-50">
                                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                                    {new Date(history.date).toLocaleDateString('tr-TR')}
                                                                </td>
                                                                <td className="px-6 py-4 text-sm text-gray-900">
                                                                    {history.description}
                                                                </td>
                                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                                    {history.orderNo}
                                                                </td>
                                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-right">
                                                                    <span className={`font-medium ${history.type === 'earned'
                                                                        ? 'text-green-600'
                                                                        : 'text-red-600'
                                                                        }`}>
                                                                        {history.type === 'earned' ? '+' : ''}{history.points} puan
                                                                    </span>
                                                                </td>
                                                            </tr>
                                                        ))}
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Bakiye Geçmişi */}
                                    <div>
                                        <h3 className="text-lg font-semibold text-gray-800 mb-4">Bakiye Geçmişi</h3>
                                        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                                            <div className="overflow-x-auto">
                                                <table className="min-w-full divide-y divide-gray-200">
                                                    <thead className="bg-gray-50">
                                                        <tr>
                                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                                Tarih
                                                            </th>
                                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                                Açıklama
                                                            </th>
                                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                                Tür
                                                            </th>
                                                            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                                Tutar
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody className="bg-white divide-y divide-gray-200">
                                                        {mockUserBalance.balanceHistory.map((history) => (
                                                            <tr key={history.id} className="hover:bg-gray-50">
                                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                                    {new Date(history.date).toLocaleDateString('tr-TR')}
                                                                </td>
                                                                <td className="px-6 py-4 text-sm text-gray-900">
                                                                    {history.description}
                                                                </td>
                                                                <td className="px-6 py-4 whitespace-nowrap">
                                                                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${history.type === 'refund'
                                                                        ? 'bg-green-100 text-green-800'
                                                                        : history.type === 'gift_card'
                                                                            ? 'bg-purple-100 text-purple-800'
                                                                            : 'bg-red-100 text-red-800'
                                                                        }`}>
                                                                        {history.type === 'refund' ? 'İade' :
                                                                            history.type === 'gift_card' ? 'Hediye Kartı' : 'Çekim'}
                                                                    </span>
                                                                </td>
                                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-right">
                                                                    <span className={`font-medium ${history.amount > 0
                                                                        ? 'text-green-600'
                                                                        : 'text-red-600'
                                                                        }`}>
                                                                        {history.amount > 0 ? '+' : ''}₺{Math.abs(history.amount).toFixed(2)}
                                                                    </span>
                                                                </td>
                                                            </tr>
                                                        ))}
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </motion.div>
                            )}

                            {activeTab === "banking" && (
                                <motion.div
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.3 }}
                                >
                                    <h2 className="text-xl font-semibold text-gray-800 mb-6">Banka Bilgileri</h2>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                        {/* IBAN Bilgileri */}
                                        <div>
                                            <div className="flex justify-between items-center mb-4">
                                                <h3 className="text-lg font-semibold text-gray-800">IBAN Bilgileri</h3>
                                                <motion.button
                                                    className="text-purple-600 text-sm font-medium flex items-center"
                                                    onClick={() => openBankingModal({
                                                        accountHolderName: mockBankingInfo.iban.accountHolderName,
                                                        bankName: mockBankingInfo.iban.bankName,
                                                        iban: mockBankingInfo.iban.iban,
                                                        branchName: mockBankingInfo.iban.branchName,
                                                        branchCode: mockBankingInfo.iban.branchCode
                                                    })}
                                                    whileHover={{ x: 2 }}
                                                >
                                                    Düzenle
                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                                    </svg>
                                                </motion.button>
                                            </div>
                                            <div className="bg-gray-50 rounded-lg p-5 space-y-4">
                                                <div>
                                                    <p className="text-sm text-black mb-1">Hesap Sahibi</p>
                                                    <p className="font-medium text-gray-600">{mockBankingInfo.iban.accountHolderName}</p>
                                                </div>
                                                <div>
                                                    <p className="text-sm text-black mb-1">Banka</p>
                                                    <p className="font-medium text-gray-600">{mockBankingInfo.iban.bankDisplayName}</p>
                                                </div>
                                                <div>
                                                    <p className="text-sm text-black mb-1">IBAN</p>
                                                    <p className="font-medium text-gray-600">{mockBankingInfo.iban.iban}</p>
                                                </div>
                                                <div>
                                                    <p className="text-sm text-black mb-1">Şube</p>
                                                    <p className="font-medium text-gray-600">{mockBankingInfo.iban.branchName} ({mockBankingInfo.iban.branchCode})</p>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Kayıtlı Kartlar */}
                                        <div>
                                            <div className="flex justify-between items-center mb-4">
                                                <h3 className="text-lg font-semibold text-gray-800">Kayıtlı Kartlar</h3>
                                                <motion.button
                                                    className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-4 py-2 rounded-lg font-medium hover:shadow-lg transition duration-300 flex items-center space-x-1"
                                                    onClick={() => openAddCardModal()}
                                                    whileHover={{ scale: 1.05 }}
                                                    whileTap={{ scale: 0.95 }}
                                                >
                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                                                    </svg>
                                                    <span>Yeni Kart</span>
                                                </motion.button>
                                            </div>

                                            <div className="space-y-3">
                                                {cards.map((card) => {
                                                    const getCardGradient = (color: string) => {
                                                        switch (color) {
                                                            case 'blue': return 'from-blue-600 to-blue-700';
                                                            case 'green': return 'from-green-600 to-green-700';
                                                            case 'purple': return 'from-purple-600 to-purple-700';
                                                            case 'orange': return 'from-orange-600 to-orange-700';
                                                            default: return 'from-gray-600 to-gray-700';
                                                        }
                                                    };

                                                    const getCardTypeDisplay = (type: string) => {
                                                        switch (type) {
                                                            case 'visa': return 'Visa';
                                                            case 'mastercard': return 'MasterCard';
                                                            case 'amex': return 'American Express';
                                                            case 'troy': return 'Troy';
                                                            default: return type;
                                                        }
                                                    };

                                                    return (
                                                        <motion.div
                                                            key={card.id}
                                                            className={`bg-gradient-to-r ${getCardGradient(card.color)} rounded-lg p-4 text-white relative cursor-pointer hover:shadow-lg transition-all duration-300`}
                                                            whileHover={{ scale: 1.02 }}
                                                            onClick={() => !card.isDefault && handleCardClick(card)}
                                                        >
                                                            <div className="flex justify-between items-start mb-3">
                                                                <div className="flex items-center space-x-2">
                                                                    <div className="text-sm opacity-90">{getCardTypeDisplay(card.cardType)}</div>
                                                                    {card.isDefault && (
                                                                        <span className="bg-white/20 text-white text-xs px-2 py-1 rounded-full font-medium">
                                                                            Varsayılan
                                                                        </span>
                                                                    )}
                                                                </div>
                                                                <div className="flex items-start space-x-2">
                                                                    {card.isDefault && (
                                                                        <motion.div className="mt-1">
                                                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-yellow-400" fill="currentColor" viewBox="0 0 24 24">
                                                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                                            </svg>
                                                                        </motion.div>
                                                                    )}
                                                                    <motion.button
                                                                        className="text-white hover:text-red-200 transition-colors p-1"
                                                                        onClick={(e) => {
                                                                            e.stopPropagation();
                                                                            // Silme işlemi
                                                                        }}
                                                                        whileHover={{ scale: 1.1 }}
                                                                    >
                                                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                                        </svg>
                                                                    </motion.button>
                                                                </div>
                                                            </div>
                                                            <div className="text-lg font-mono mb-4 tracking-wider">{card.cardNumber}</div>
                                                            <div className="flex justify-between items-end text-sm opacity-90">
                                                                <span>{card.cardHolderName}</span>
                                                                <div className="flex flex-col items-end">
                                                                    <span>{card.expirationDate}</span>
                                                                    {!card.isDefault && (
                                                                        <span className="text-xs bg-white/10 px-2 py-1 rounded-full mt-1 opacity-100 text-white">
                                                                            Varsayılan yap
                                                                        </span>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        </motion.div>
                                                    );
                                                })}
                                            </div>
                                        </div>
                                    </div>

                                    {/* İstatistikler */}
                                    <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
                                        <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                                            <div className="flex items-center">
                                                <div className="bg-green-500 p-2 rounded-full mr-3">
                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                                    </svg>
                                                </div>
                                                <div>
                                                    <div className="text-2xl font-bold text-green-600">₺{mockBankingInfo.statistics.monthlyEarnings.toLocaleString()}</div>
                                                    <div className="text-green-700 text-sm">Bu Ay Kazanç</div>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                                            <div className="flex items-center">
                                                <div className="bg-blue-500 p-2 rounded-full mr-3">
                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                                                    </svg>
                                                </div>
                                                <div>
                                                    <div className="text-2xl font-bold text-blue-600">{mockBankingInfo.statistics.totalTransactions}</div>
                                                    <div className="text-blue-700 text-sm">Toplam İşlem</div>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
                                            <div className="flex items-center">
                                                <div className="bg-purple-500 p-2 rounded-full mr-3">
                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                                    </svg>
                                                </div>
                                                <div>
                                                    <div className="text-2xl font-bold text-purple-600">₺{mockBankingInfo.statistics.averageTransaction}</div>
                                                    <div className="text-purple-700 text-sm">Ortalama İşlem</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </motion.div>
                            )}

                            {activeTab === "settings" && (
                                <motion.div
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.3 }}
                                >
                                    <h2 className="text-xl font-semibold text-gray-800 mb-6">Hesap Ayarları</h2>

                                    <div className="space-y-8">
                                        <div className="border-b border-gray-200 pb-6">
                                            <h3 className="text-lg font-medium text-gray-900 mb-4">Şifre Değiştir</h3>
                                            <form className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                <div>
                                                    <label htmlFor="current-password" className="block text-sm font-medium text-gray-700 mb-1">
                                                        Mevcut Şifre
                                                    </label>
                                                    <input
                                                        type="password"
                                                        id="current-password"
                                                        className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black"
                                                        placeholder="Mevcut şifrenizi girin"
                                                    />
                                                </div>
                                                <div className="md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-4">
                                                    <div>
                                                        <label htmlFor="new-password" className="block text-sm font-medium text-gray-700 mb-1">
                                                            Yeni Şifre
                                                        </label>
                                                        <input
                                                            type="password"
                                                            id="new-password"
                                                            className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black"
                                                            placeholder="Yeni şifrenizi girin"
                                                        />
                                                    </div>
                                                    <div>
                                                        <label htmlFor="confirm-password" className="block text-sm font-medium text-gray-700 mb-1">
                                                            Şifre Tekrar
                                                        </label>
                                                        <input
                                                            type="password"
                                                            id="confirm-password"
                                                            className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition text-black"
                                                            placeholder="Şifrenizi tekrar girin"
                                                        />
                                                    </div>
                                                </div>
                                                <div>
                                                    <motion.button
                                                        type="submit"
                                                        className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-2.5 rounded-lg font-medium hover:shadow-lg transition duration-300"
                                                        whileHover={{ scale: 1.05 }}
                                                        whileTap={{ scale: 0.95 }}
                                                    >
                                                        Şifreyi Güncelle
                                                    </motion.button>
                                                </div>
                                            </form>
                                        </div>

                                        <div className="border-b border-gray-200 pb-6">
                                            <h3 className="text-lg font-medium text-gray-900 mb-4">E-posta Abonelikleri</h3>
                                            <div className="space-y-4">
                                                <div className="flex items-start">
                                                    <div className="flex items-center h-5">
                                                        <input
                                                            id="newsletter"
                                                            type="checkbox"
                                                            defaultChecked
                                                            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                                                        />
                                                    </div>
                                                    <div className="ml-3 text-sm">
                                                        <label htmlFor="newsletter" className="font-medium text-gray-700">
                                                            Haberler ve Promosyonlar
                                                        </label>
                                                        <p className="text-gray-500">Yeni ürünler, özel teklifler ve indirimlerden haberdar olun</p>
                                                    </div>
                                                </div>
                                                <div className="flex items-start">
                                                    <div className="flex items-center h-5">
                                                        <input
                                                            id="product-updates"
                                                            type="checkbox"
                                                            defaultChecked
                                                            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                                                        />
                                                    </div>
                                                    <div className="ml-3 text-sm">
                                                        <label htmlFor="product-updates" className="font-medium text-gray-700">
                                                            Ürün Güncellemeleri
                                                        </label>
                                                        <p className="text-gray-500">Favorilediğiniz veya satın aldığınız ürünlerle ilgili güncellemeler alın</p>
                                                    </div>
                                                </div>
                                                <div className="mt-4">
                                                    <motion.button
                                                        className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-2.5 rounded-lg font-medium hover:shadow-lg transition duration-300"
                                                        whileHover={{ scale: 1.05 }}
                                                        whileTap={{ scale: 0.95 }}
                                                    >
                                                        Tercihleri Kaydet
                                                    </motion.button>
                                                </div>
                                            </div>
                                        </div>

                                        <div>
                                            <h3 className="text-lg font-medium text-gray-900 mb-4">Hesap İşlemleri</h3>
                                            <div className="space-y-4">
                                                <motion.button
                                                    className="text-red-600 hover:text-red-800 font-medium flex items-center"
                                                    whileHover={{ x: 2 }}
                                                >
                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                    </svg>
                                                    Hesabımı Sil
                                                </motion.button>
                                            </div>
                                        </div>
                                    </div>
                                </motion.div>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* Modals */}
            <EditPersonalInfoModal />

            <AddAddressModal
                onAddressAdded={() => {
                    // Address listesini refresh etmek için gerekirse buraya kod eklenebilir
                    console.log('✅ Address added successfully');
                }}
            />

            <FavoriteModal
                isOpen={showFavoriteModal}
                onClose={() => setShowFavoriteModal(false)}
                product={favoriteModalProduct}
                isAdded={false}
            />

            <BankingModal />

            <AddCardModal />

            <SetDefaultCardModal />

            <ReferenceRegistrationModal
                onSubmit={handleReferenceRegistration}
            />
        </div>
    );
}

export default function AccountPage() {
    return (
        <Suspense fallback={<div>Loading...</div>}>
            <AccountPageContent />
        </Suspense>
    );
}