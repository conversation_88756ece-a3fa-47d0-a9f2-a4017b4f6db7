"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app-pages-internals",{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next/dist/client/components/layout-router.js ***!
  \*******************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return OuterLayoutRouter;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _reactdom = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _fetchserverresponse = __webpack_require__(/*! ./router-reducer/fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _unresolvedthenable = __webpack_require__(/*! ./unresolved-thenable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/unresolved-thenable.js\");\nconst _errorboundary = __webpack_require__(/*! ./error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\");\nconst _matchsegments = __webpack_require__(/*! ./match-segments */ \"(app-pages-browser)/./node_modules/next/dist/client/components/match-segments.js\");\nconst _handlesmoothscroll = __webpack_require__(/*! ../../shared/lib/router/utils/handle-smooth-scroll */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\");\nconst _redirectboundary = __webpack_require__(/*! ./redirect-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js\");\nconst _errorboundary1 = __webpack_require__(/*! ./http-access-fallback/error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\");\nconst _createroutercachekey = __webpack_require__(/*! ./router-reducer/create-router-cache-key */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-router-cache-key.js\");\nconst _hasinterceptionrouteincurrenttree = __webpack_require__(/*! ./router-reducer/reducers/has-interception-route-in-current-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js\");\nconst _useactionqueue = __webpack_require__(/*! ./use-action-queue */ \"(app-pages-browser)/./node_modules/next/dist/client/components/use-action-queue.js\");\n/**\n * Add refetch marker to router state at the point of the current layout segment.\n * This ensures the response returned is not further down than the current layout segment.\n */ function walkAddRefetch(segmentPathToWalk, treeToRecreate) {\n    if (segmentPathToWalk) {\n        const [segment, parallelRouteKey] = segmentPathToWalk;\n        const isLast = segmentPathToWalk.length === 2;\n        if ((0, _matchsegments.matchSegment)(treeToRecreate[0], segment)) {\n            if (treeToRecreate[1].hasOwnProperty(parallelRouteKey)) {\n                if (isLast) {\n                    const subTree = walkAddRefetch(undefined, treeToRecreate[1][parallelRouteKey]);\n                    return [\n                        treeToRecreate[0],\n                        {\n                            ...treeToRecreate[1],\n                            [parallelRouteKey]: [\n                                subTree[0],\n                                subTree[1],\n                                subTree[2],\n                                'refetch'\n                            ]\n                        }\n                    ];\n                }\n                return [\n                    treeToRecreate[0],\n                    {\n                        ...treeToRecreate[1],\n                        [parallelRouteKey]: walkAddRefetch(segmentPathToWalk.slice(2), treeToRecreate[1][parallelRouteKey])\n                    }\n                ];\n            }\n        }\n    }\n    return treeToRecreate;\n}\nconst __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE = _reactdom.default.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;\n// TODO-APP: Replace with new React API for finding dom nodes without a `ref` when available\n/**\n * Wraps ReactDOM.findDOMNode with additional logic to hide React Strict Mode warning\n */ function findDOMNode(instance) {\n    // Tree-shake for server bundle\n    if (false) {}\n    // __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode is null during module init.\n    // We need to lazily reference it.\n    const internal_reactDOMfindDOMNode = __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode;\n    return internal_reactDOMfindDOMNode(instance);\n}\nconst rectProperties = [\n    'bottom',\n    'height',\n    'left',\n    'right',\n    'top',\n    'width',\n    'x',\n    'y'\n];\n/**\n * Check if a HTMLElement is hidden or fixed/sticky position\n */ function shouldSkipElement(element) {\n    // we ignore fixed or sticky positioned elements since they'll likely pass the \"in-viewport\" check\n    // and will result in a situation we bail on scroll because of something like a fixed nav,\n    // even though the actual page content is offscreen\n    if ([\n        'sticky',\n        'fixed'\n    ].includes(getComputedStyle(element).position)) {\n        if (true) {\n            console.warn('Skipping auto-scroll behavior due to `position: sticky` or `position: fixed` on element:', element);\n        }\n        return true;\n    }\n    // Uses `getBoundingClientRect` to check if the element is hidden instead of `offsetParent`\n    // because `offsetParent` doesn't consider document/body\n    const rect = element.getBoundingClientRect();\n    return rectProperties.every((item)=>rect[item] === 0);\n}\n/**\n * Check if the top corner of the HTMLElement is in the viewport.\n */ function topOfElementInViewport(element, viewportHeight) {\n    const rect = element.getBoundingClientRect();\n    return rect.top >= 0 && rect.top <= viewportHeight;\n}\n/**\n * Find the DOM node for a hash fragment.\n * If `top` the page has to scroll to the top of the page. This mirrors the browser's behavior.\n * If the hash fragment is an id, the page has to scroll to the element with that id.\n * If the hash fragment is a name, the page has to scroll to the first element with that name.\n */ function getHashFragmentDomNode(hashFragment) {\n    // If the hash fragment is `top` the page has to scroll to the top of the page.\n    if (hashFragment === 'top') {\n        return document.body;\n    }\n    var _document_getElementById;\n    // If the hash fragment is an id, the page has to scroll to the element with that id.\n    return (_document_getElementById = document.getElementById(hashFragment)) != null ? _document_getElementById : document.getElementsByName(hashFragment)[0];\n}\nclass InnerScrollAndFocusHandler extends _react.default.Component {\n    componentDidMount() {\n        this.handlePotentialScroll();\n    }\n    componentDidUpdate() {\n        // Because this property is overwritten in handlePotentialScroll it's fine to always run it when true as it'll be set to false for subsequent renders.\n        if (this.props.focusAndScrollRef.apply) {\n            this.handlePotentialScroll();\n        }\n    }\n    render() {\n        return this.props.children;\n    }\n    constructor(...args){\n        super(...args), this.handlePotentialScroll = ()=>{\n            // Handle scroll and focus, it's only applied once in the first useEffect that triggers that changed.\n            const { focusAndScrollRef, segmentPath } = this.props;\n            if (focusAndScrollRef.apply) {\n                // segmentPaths is an array of segment paths that should be scrolled to\n                // if the current segment path is not in the array, the scroll is not applied\n                // unless the array is empty, in which case the scroll is always applied\n                if (focusAndScrollRef.segmentPaths.length !== 0 && !focusAndScrollRef.segmentPaths.some((scrollRefSegmentPath)=>segmentPath.every((segment, index)=>(0, _matchsegments.matchSegment)(segment, scrollRefSegmentPath[index])))) {\n                    return;\n                }\n                let domNode = null;\n                const hashFragment = focusAndScrollRef.hashFragment;\n                if (hashFragment) {\n                    domNode = getHashFragmentDomNode(hashFragment);\n                }\n                // `findDOMNode` is tricky because it returns just the first child if the component is a fragment.\n                // This already caused a bug where the first child was a <link/> in head.\n                if (!domNode) {\n                    domNode = findDOMNode(this);\n                }\n                // If there is no DOM node this layout-router level is skipped. It'll be handled higher-up in the tree.\n                if (!(domNode instanceof Element)) {\n                    return;\n                }\n                // Verify if the element is a HTMLElement and if we want to consider it for scroll behavior.\n                // If the element is skipped, try to select the next sibling and try again.\n                while(!(domNode instanceof HTMLElement) || shouldSkipElement(domNode)){\n                    if (true) {\n                        var _domNode_parentElement;\n                        if (((_domNode_parentElement = domNode.parentElement) == null ? void 0 : _domNode_parentElement.localName) === 'head') {\n                        // TODO: We enter this state when metadata was rendered as part of the page or via Next.js.\n                        // This is always a bug in Next.js and caused by React hoisting metadata.\n                        // We need to replace `findDOMNode` in favor of Fragment Refs (when available) so that we can skip over metadata.\n                        }\n                    }\n                    // No siblings found that match the criteria are found, so handle scroll higher up in the tree instead.\n                    if (domNode.nextElementSibling === null) {\n                        return;\n                    }\n                    domNode = domNode.nextElementSibling;\n                }\n                // State is mutated to ensure that the focus and scroll is applied only once.\n                focusAndScrollRef.apply = false;\n                focusAndScrollRef.hashFragment = null;\n                focusAndScrollRef.segmentPaths = [];\n                (0, _handlesmoothscroll.handleSmoothScroll)(()=>{\n                    // In case of hash scroll, we only need to scroll the element into view\n                    if (hashFragment) {\n                        ;\n                        domNode.scrollIntoView();\n                        return;\n                    }\n                    // Store the current viewport height because reading `clientHeight` causes a reflow,\n                    // and it won't change during this function.\n                    const htmlElement = document.documentElement;\n                    const viewportHeight = htmlElement.clientHeight;\n                    // If the element's top edge is already in the viewport, exit early.\n                    if (topOfElementInViewport(domNode, viewportHeight)) {\n                        return;\n                    }\n                    // Otherwise, try scrolling go the top of the document to be backward compatible with pages\n                    // scrollIntoView() called on `<html/>` element scrolls horizontally on chrome and firefox (that shouldn't happen)\n                    // We could use it to scroll horizontally following RTL but that also seems to be broken - it will always scroll left\n                    // scrollLeft = 0 also seems to ignore RTL and manually checking for RTL is too much hassle so we will scroll just vertically\n                    htmlElement.scrollTop = 0;\n                    // Scroll to domNode if domNode is not in viewport when scrolled to top of document\n                    if (!topOfElementInViewport(domNode, viewportHeight)) {\n                        // Scroll into view doesn't scroll horizontally by default when not needed\n                        ;\n                        domNode.scrollIntoView();\n                    }\n                }, {\n                    // We will force layout by querying domNode position\n                    dontForceLayout: true,\n                    onlyHashChange: focusAndScrollRef.onlyHashChange\n                });\n                // Mutate after scrolling so that it can be read by `handleSmoothScroll`\n                focusAndScrollRef.onlyHashChange = false;\n                // Set focus on the element\n                domNode.focus();\n            }\n        };\n    }\n}\nfunction ScrollAndFocusHandler(param) {\n    let { segmentPath, children } = param;\n    const context = (0, _react.useContext)(_approutercontextsharedruntime.GlobalLayoutRouterContext);\n    if (!context) {\n        throw Object.defineProperty(new Error('invariant global layout router not mounted'), \"__NEXT_ERROR_CODE\", {\n            value: \"E473\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(InnerScrollAndFocusHandler, {\n        segmentPath: segmentPath,\n        focusAndScrollRef: context.focusAndScrollRef,\n        children: children\n    });\n}\n_c = ScrollAndFocusHandler;\n/**\n * InnerLayoutRouter handles rendering the provided segment based on the cache.\n */ function InnerLayoutRouter(param) {\n    let { tree, segmentPath, cacheNode, url } = param;\n    const context = (0, _react.useContext)(_approutercontextsharedruntime.GlobalLayoutRouterContext);\n    if (!context) {\n        throw Object.defineProperty(new Error('invariant global layout router not mounted'), \"__NEXT_ERROR_CODE\", {\n            value: \"E473\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    const { tree: fullTree } = context;\n    // `rsc` represents the renderable node for this segment.\n    // If this segment has a `prefetchRsc`, it's the statically prefetched data.\n    // We should use that on initial render instead of `rsc`. Then we'll switch\n    // to `rsc` when the dynamic response streams in.\n    //\n    // If no prefetch data is available, then we go straight to rendering `rsc`.\n    const resolvedPrefetchRsc = cacheNode.prefetchRsc !== null ? cacheNode.prefetchRsc : cacheNode.rsc;\n    // We use `useDeferredValue` to handle switching between the prefetched and\n    // final values. The second argument is returned on initial render, then it\n    // re-renders with the first argument.\n    const rsc = (0, _react.useDeferredValue)(cacheNode.rsc, resolvedPrefetchRsc);\n    // `rsc` is either a React node or a promise for a React node, except we\n    // special case `null` to represent that this segment's data is missing. If\n    // it's a promise, we need to unwrap it so we can determine whether or not the\n    // data is missing.\n    const resolvedRsc = typeof rsc === 'object' && rsc !== null && typeof rsc.then === 'function' ? (0, _react.use)(rsc) : rsc;\n    if (!resolvedRsc) {\n        // The data for this segment is not available, and there's no pending\n        // navigation that will be able to fulfill it. We need to fetch more from\n        // the server and patch the cache.\n        // Check if there's already a pending request.\n        let lazyData = cacheNode.lazyData;\n        if (lazyData === null) {\n            /**\n       * Router state with refetch marker added\n       */ // TODO-APP: remove ''\n            const refetchTree = walkAddRefetch([\n                '',\n                ...segmentPath\n            ], fullTree);\n            const includeNextUrl = (0, _hasinterceptionrouteincurrenttree.hasInterceptionRouteInCurrentTree)(fullTree);\n            const navigatedAt = Date.now();\n            cacheNode.lazyData = lazyData = (0, _fetchserverresponse.fetchServerResponse)(new URL(url, location.origin), {\n                flightRouterState: refetchTree,\n                nextUrl: includeNextUrl ? context.nextUrl : null\n            }).then((serverResponse)=>{\n                (0, _react.startTransition)(()=>{\n                    (0, _useactionqueue.dispatchAppRouterAction)({\n                        type: _routerreducertypes.ACTION_SERVER_PATCH,\n                        previousTree: fullTree,\n                        serverResponse,\n                        navigatedAt\n                    });\n                });\n                return serverResponse;\n            });\n            // Suspend while waiting for lazyData to resolve\n            (0, _react.use)(lazyData);\n        }\n        // Suspend infinitely as `changeByServerResponse` will cause a different part of the tree to be rendered.\n        // A falsey `resolvedRsc` indicates missing data -- we should not commit that branch, and we need to wait for the data to arrive.\n        (0, _react.use)(_unresolvedthenable.unresolvedThenable);\n    }\n    // If we get to this point, then we know we have something we can render.\n    const subtree = /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.LayoutRouterContext.Provider, {\n        value: {\n            parentTree: tree,\n            parentCacheNode: cacheNode,\n            parentSegmentPath: segmentPath,\n            // TODO-APP: overriding of url for parallel routes\n            url: url\n        },\n        children: resolvedRsc\n    });\n    // Ensure root layout is not wrapped in a div as the root layout renders `<html>`\n    return subtree;\n}\n_c1 = InnerLayoutRouter;\n/**\n * Renders suspense boundary with the provided \"loading\" property as the fallback.\n * If no loading property is provided it renders the children without a suspense boundary.\n */ function LoadingBoundary(param) {\n    let { loading, children } = param;\n    // If loading is a promise, unwrap it. This happens in cases where we haven't\n    // yet received the loading data from the server — which includes whether or\n    // not this layout has a loading component at all.\n    //\n    // It's OK to suspend here instead of inside the fallback because this\n    // promise will resolve simultaneously with the data for the segment itself.\n    // So it will never suspend for longer than it would have if we didn't use\n    // a Suspense fallback at all.\n    let loadingModuleData;\n    if (typeof loading === 'object' && loading !== null && typeof loading.then === 'function') {\n        const promiseForLoading = loading;\n        loadingModuleData = (0, _react.use)(promiseForLoading);\n    } else {\n        loadingModuleData = loading;\n    }\n    if (loadingModuleData) {\n        const loadingRsc = loadingModuleData[0];\n        const loadingStyles = loadingModuleData[1];\n        const loadingScripts = loadingModuleData[2];\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.Suspense, {\n            fallback: /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    loadingStyles,\n                    loadingScripts,\n                    loadingRsc\n                ]\n            }),\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: children\n    });\n}\n_c2 = LoadingBoundary;\nfunction OuterLayoutRouter(param) {\n    let { parallelRouterKey, error, errorStyles, errorScripts, templateStyles, templateScripts, template, notFound, forbidden, unauthorized } = param;\n    const context = (0, _react.useContext)(_approutercontextsharedruntime.LayoutRouterContext);\n    if (!context) {\n        throw Object.defineProperty(new Error('invariant expected layout router to be mounted'), \"__NEXT_ERROR_CODE\", {\n            value: \"E56\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    const { parentTree, parentCacheNode, parentSegmentPath, url } = context;\n    // Get the CacheNode for this segment by reading it from the parent segment's\n    // child map.\n    const parentParallelRoutes = parentCacheNode.parallelRoutes;\n    let segmentMap = parentParallelRoutes.get(parallelRouterKey);\n    // If the parallel router cache node does not exist yet, create it.\n    // This writes to the cache when there is no item in the cache yet. It never *overwrites* existing cache items which is why it's safe in concurrent mode.\n    if (!segmentMap) {\n        segmentMap = new Map();\n        parentParallelRoutes.set(parallelRouterKey, segmentMap);\n    }\n    // Get the active segment in the tree\n    // The reason arrays are used in the data format is that these are transferred from the server to the browser so it's optimized to save bytes.\n    const parentTreeSegment = parentTree[0];\n    const tree = parentTree[1][parallelRouterKey];\n    const treeSegment = tree[0];\n    const segmentPath = parentSegmentPath === null ? // the code. We should clean this up.\n    [\n        parallelRouterKey\n    ] : parentSegmentPath.concat([\n        parentTreeSegment,\n        parallelRouterKey\n    ]);\n    // The \"state\" key of a segment is the one passed to React — it represents the\n    // identity of the UI tree. Whenever the state key changes, the tree is\n    // recreated and the state is reset. In the App Router model, search params do\n    // not cause state to be lost, so two segments with the same segment path but\n    // different search params should have the same state key.\n    //\n    // The \"cache\" key of a segment, however, *does* include the search params, if\n    // it's possible that the segment accessed the search params on the server.\n    // (This only applies to page segments; layout segments cannot access search\n    // params on the server.)\n    const cacheKey = (0, _createroutercachekey.createRouterCacheKey)(treeSegment);\n    const stateKey = (0, _createroutercachekey.createRouterCacheKey)(treeSegment, true) // no search params\n    ;\n    // Read segment path from the parallel router cache node.\n    let cacheNode = segmentMap.get(cacheKey);\n    if (cacheNode === undefined) {\n        // When data is not available during rendering client-side we need to fetch\n        // it from the server.\n        const newLazyCacheNode = {\n            lazyData: null,\n            rsc: null,\n            prefetchRsc: null,\n            head: null,\n            prefetchHead: null,\n            parallelRoutes: new Map(),\n            loading: null,\n            navigatedAt: -1\n        };\n        // Flight data fetch kicked off during render and put into the cache.\n        cacheNode = newLazyCacheNode;\n        segmentMap.set(cacheKey, newLazyCacheNode);\n    }\n    /*\n    - Error boundary\n      - Only renders error boundary if error component is provided.\n      - Rendered for each segment to ensure they have their own error state.\n    - Loading boundary\n      - Only renders suspense boundary if loading components is provided.\n      - Rendered for each segment to ensure they have their own loading state.\n      - Passed to the router during rendering to ensure it can be immediately rendered when suspending on a Flight fetch.\n  */ // TODO: The loading module data for a segment is stored on the parent, then\n    // applied to each of that parent segment's parallel route slots. In the\n    // simple case where there's only one parallel route (the `children` slot),\n    // this is no different from if the loading module data where stored on the\n    // child directly. But I'm not sure this actually makes sense when there are\n    // multiple parallel routes. It's not a huge issue because you always have\n    // the option to define a narrower loading boundary for a particular slot. But\n    // this sort of smells like an implementation accident to me.\n    const loadingModuleData = parentCacheNode.loading;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_approutercontextsharedruntime.TemplateContext.Provider, {\n        value: /*#__PURE__*/ (0, _jsxruntime.jsx)(ScrollAndFocusHandler, {\n            segmentPath: segmentPath,\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorboundary.ErrorBoundary, {\n                errorComponent: error,\n                errorStyles: errorStyles,\n                errorScripts: errorScripts,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(LoadingBoundary, {\n                    loading: loadingModuleData,\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorboundary1.HTTPAccessFallbackBoundary, {\n                        notFound: notFound,\n                        forbidden: forbidden,\n                        unauthorized: unauthorized,\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_redirectboundary.RedirectBoundary, {\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(InnerLayoutRouter, {\n                                url: url,\n                                tree: tree,\n                                cacheNode: cacheNode,\n                                segmentPath: segmentPath\n                            })\n                        })\n                    })\n                })\n            })\n        }),\n        children: [\n            templateStyles,\n            templateScripts,\n            template\n        ]\n    }, stateKey);\n}\n_c3 = OuterLayoutRouter;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=layout-router.js.map\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ScrollAndFocusHandler\");\n$RefreshReg$(_c1, \"InnerLayoutRouter\");\n$RefreshReg$(_c2, \"LoadingBoundary\");\n$RefreshReg$(_c3, \"OuterLayoutRouter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js\n"));

/***/ })

});