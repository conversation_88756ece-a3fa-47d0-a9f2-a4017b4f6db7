import apiClient from './api';
import { API_ENDPOINTS } from '@/constants/apiEndpoints';

// API Request/Response tipleri
export interface LoginRequest {
    email: string;
    password: string;
}

export interface RegisterRequest {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    phoneNumber: string;
    referansCode?: string;
}

export interface BackendLoginResponse {
    status: number;
    message: string;
    data?: {
        token?: string;
        refreshToken?: string;
        careerId?: number;
        membershipLevelId?: number;
        user?: any;
    };
}

export interface LoginResponse {
    success: boolean;
    message: string;
    user?: any;
}

export interface RegisterResponse {
    status: number;
    message: string;
    data?: any;
}

export interface BackendRegisterResponse {
    success: boolean;
    message: string;
    user?: {
        id: number;
        firstName: string;
        lastName: string;
        email: string;
        phoneNumber: string;
    };
}

export interface AddReferenceRequest {
    referansCode: string;
}

// Profile güncelleme için tipler
export interface UpdateProfileRequest {
    firstName?: string;
    lastName?: string;
    dateOfBirth?: string; // ISO date string
    gender?: number; // 0: Unspecified, 1: Male, 2: Female, 3: Other
    location?: string;
    phoneNumber?: string;
}

// Profil bilgileri response tipi - API response'una göre güncellenmiş
export interface ProfileInfoResponse {
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber?: string; // API'den gelmiyor, optional yapıldı
    dateOfBirth?: string | null;
    gender?: number | null;
    location?: string | null;
    profilePictureUrl?: string | null;
    registeredAt: string;
    membershipLevelId: number; // Backend'den membershipLevelId olarak geliyor
    careerRankId: number; // Backend'den careerRankId olarak geliyor
}

// API Response wrapper tipi
interface ApiResponse<T> {
    status: number;
    message: string;
    data: T;
}

// Authentication Services
export const authService = {
    // Clear authentication cookies
    clearAuthCookies(): void {
        if (typeof window !== 'undefined') {
            console.log('🧹 Auth cookieleri temizleniyor...');

            // Bilinen auth cookieleri temizle
            const authCookies = ['AccessToken', 'RefreshToken', 'AuthToken', 'Token'];

            authCookies.forEach(cookieName => {
                // Farklı pathlerde temizle
                document.cookie = cookieName + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/';
                document.cookie = cookieName + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=' + window.location.hostname;
                document.cookie = cookieName + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.' + window.location.hostname;
            });

            console.log('🧹 Auth cookieleri temizlendi');
        }
    },
    // Login
    async login(email: string, password: string): Promise<boolean> {
        console.log('🚪 AuthService login başlıyor...', { email });

        // Login başlangıcında logout flag'ini kontrol et ve temizle
        const hasLoggedOut = typeof window !== 'undefined' ? localStorage.getItem('hasLoggedOut') : null;
        console.log('🔍 Login başlangıcında logout flag kontrolü:', { hasLoggedOut });

        if (hasLoggedOut) {
            console.log('🧹 Eski logout flag temizleniyor (yeni login)');
            if (typeof window !== 'undefined') {
                localStorage.removeItem('hasLoggedOut');
            }
        }

        const loginData = {
            email,
            password
        };

        console.log('📤 Gönderilecek veri:', loginData);
        console.log('📡 API URL:', apiClient.defaults.baseURL);
        console.log('🔧 API Config:', {
            baseURL: apiClient.defaults.baseURL,
            withCredentials: apiClient.defaults.withCredentials,
            headers: apiClient.defaults.headers
        });

        try {
            console.log(`📡 POST ${API_ENDPOINTS.LOGIN} çağrısı yapılıyor...`);
            const response = await apiClient.post(API_ENDPOINTS.LOGIN, loginData);

            console.log('📡 Login response:', response.data);
            console.log('📊 Response status:', response.status);
            console.log('🍪 Response headers:', response.headers);

            // Backend Set-Cookie ile token gönderdi
            console.log('🍪 Backend Set-Cookie ile token gönderdi');

            // Response structure debug
            console.log('🔍 Response structure debug:', {
                data: response.data,
                status: response.status,
                message: response.data?.message,
                hasData: !!response.data,
                statusType: response.data?.statusType,
                dataKeys: response.data ? Object.keys(response.data) : []
            });

            // GÜÇLÜ SUCCESS DETECTION
            // HTTP status 200 = Backend başarılı response verdi
            // Set-Cookie header varlığı = Backend token gönderdi
            const httpSuccess = response.status === 200;
            const hasSetCookie = response.headers['set-cookie'] ||
                response.headers['Set-Cookie'] ||
                response.headers['SET-COOKIE'];

            // Çeşitli backend response formatlarını kontrol et
            const messageSuccess = response.data?.message === 'Giriş başarılı.' ||
                response.data?.message === 'Login successful' ||
                response.data?.message?.includes('başarılı') ||
                response.data?.message?.includes('successful');

            const statusSuccess = response.data?.status === 0 ||
                response.data?.status === 200 ||
                response.data?.success === true;

            // Eğer HTTP 200 dönmüş ve hata mesajı YOK ise başarılı sayalım
            const noErrorMessage = !response.data?.message?.includes('hatalı') &&
                !response.data?.message?.includes('error') &&
                !response.data?.message?.includes('failed') &&
                !response.data?.message?.includes('invalid') &&
                !response.data?.error;

            const isSuccess = httpSuccess && (messageSuccess || statusSuccess || noErrorMessage);

            console.log('🔍 Success detection:', {
                httpSuccess,
                hasSetCookie: !!hasSetCookie,
                messageSuccess,
                statusSuccess,
                noErrorMessage,
                finalSuccess: isSuccess
            });

            if (isSuccess) {
                console.log('✅ Login başarılı!');
                console.log('🍪 Backend Set-Cookie header ile token gönderdi');
                console.log('🔄 Cookie browser tarafından otomatik set edilecek');

                // Backend zaten HttpOnly cookie set ediyor, biz manuel set etmeye gerek yok
                // withCredentials: true olduğu için sonraki API çağrıları cookie'yi otomatik gönderecek

                return true;
            } else {
                console.log('❌ Login başarısız - Response criteria not met');

                // Başarısız login durumunda eski cookie'leri temizle
                this.clearAuthCookies();

                return false;
            }
        } catch (error: any) {
            throw error;
        }
    },

    // Register
    async register(userData: RegisterRequest): Promise<BackendRegisterResponse> {
        console.log('📤 Register request:', userData);

        try {
            const response = await apiClient.post(API_ENDPOINTS.REGISTER, userData);
            console.log('✅ Register response:', response.data);

            const backendResponse: RegisterResponse = response.data;

            return {
                success: backendResponse.status === 0,
                message: backendResponse.message,
                user: backendResponse.data ? {
                    id: 0, // Backend'den user detayları gelmediği için placeholder
                    firstName: userData.firstName,
                    lastName: userData.lastName,
                    email: userData.email,
                    phoneNumber: userData.phoneNumber || ''
                } : undefined
            };
        } catch (error: any) {
            console.error('❌ Register error:', error);
            throw error;
        }
    },

    // Refresh Token
    async refreshToken(): Promise<any> {
        const response = await apiClient.get(API_ENDPOINTS.REFRESH_TOKEN);
        return response.data;
    },

    // Logout
    async logout(): Promise<void> {
        try {
            // Backend logout çağrısı
            await apiClient.get(API_ENDPOINTS.LOGOUT);
            console.log('✅ Backend logout başarılı');
        } catch (error) {
            console.error('❌ Backend logout hatası:', error);
            // Backend logout başarısız olsa bile yerel temizlik yap
        } finally {
            if (typeof window !== 'undefined') {
                // localStorage'ı tamamen temizle
                localStorage.clear();
                console.log('🧹 localStorage temizlendi');

                // Cookie'leri manuel olarak temizle (mümkün olanları)
                const cookies = document.cookie.split(";");
                for (let cookie of cookies) {
                    const eqPos = cookie.indexOf("=");
                    const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
                    if (name) {
                        // Cookie'yi farklı path'lerde temizle
                        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
                        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${window.location.hostname}`;
                        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.${window.location.hostname}`;
                    }
                }
                console.log('🍪 Cookieler temizlendi');

                // Session storage'ı da temizle
                sessionStorage.clear();
                console.log('🧹 sessionStorage temizlendi');
            }
        }
    },

    // Get User Info - HttpOnly cookie ile çalışır
    async getUserInfo(): Promise<any> {
        console.log('🔎 AuthService getUserInfo başlıyor...');
        console.log('🍪 withCredentials ile cookie otomatik gönderilecek');

        try {
            // Sadece ana USER_INFO endpoint'ini dene.
            const response = await apiClient.get(API_ENDPOINTS.USER_INFO);
            console.log('✅ getUserInfo başarılı:', response.data);
            return response.data;
        } catch (error: any) {
            // Eğer hata 401 (Unauthorized) ise, bu bir çökme hatası değil,
            // sadece kullanıcının giriş yapmadığı anlamına gelir.
            // Bu durumu hata olarak fırlatmak yerine null döndürerek sessizce yönetiyoruz.
            if (error.response?.status === 401) {
                console.log('ℹ️ Kullanıcı giriş yapmamış. (Bu beklenen bir durumdur)');
                return null;
            }

            // Diğer tüm hatalar (500, ağ hataları vs.) gerçek bir sorundur.
            // Bunları React Query'nin yakalaması için fırlatıyoruz.
            console.error('❌ getUserInfo sırasında beklenmedik bir hata oluştu:', error);
            throw error;
        }
    },

    // Add Reference Code
    async addReference(referenceCode: string): Promise<void> {
        try {
            await apiClient.post(API_ENDPOINTS.ADD_REFERENCE, { referansCode: referenceCode });
        } catch (error) {
            console.error('❌ Referans eklenirken hata:', error);
            throw error;
        }
    },

    // Get My Reference Code
    async getMyRefCode(): Promise<{ code: string }> {
        try {
            console.log('📤 Referans kodu alınıyor...');
            const response = await apiClient.get(API_ENDPOINTS.MY_REF_CODE);
            console.log('✅ Referans kodu başarıyla alındı:', response.data);

            // API response: { status: 0, message: "", data: { code: "TR-241600" } }
            if (response.data.status === 0 && response.data.data) {
                return response.data.data;
            }

            throw new Error(response.data.message || 'Referans kodu alınamadı');
        } catch (error: any) {
            console.error('❌ Referans kodu alınırken hata:', error);
            throw error;
        }
    },

    // Make Admin (Admin only)
    async makeAdmin(userIdOrEmail: string): Promise<void> {
        try {
            await apiClient.post(API_ENDPOINTS.MAKE_ADMIN, { userIdOrEmail });
        } catch (error) {
            console.error('❌ Admin yapma hatası:', error);
            throw error;
        }
    },

    // Test endpoint
    async test(): Promise<any> {
        const response = await apiClient.get(API_ENDPOINTS.TEST_AUTH);
        return response.data;
    },

    // Debug Claims
    async debugClaims(): Promise<any> {
        const response = await apiClient.get(API_ENDPOINTS.DEBUG_CLAIMS);
        return response.data;
    },

    // Get Profile Info - Detaylı profil bilgileri
    async getProfileInfo(): Promise<ProfileInfoResponse> {
        console.log('🔎 AuthService getProfileInfo başlıyor...');

        try {
            const response = await apiClient.get<ApiResponse<ProfileInfoResponse>>(API_ENDPOINTS.PROFILE_INFO);
            console.log('✅ getProfileInfo raw response:', response.data);

            // API response'u data wrapper'ı içinde geliyor
            if (response.data.status === 0 && response.data.data) {
                console.log('✅ getProfileInfo başarılı:', response.data.data);
                return response.data.data;
            } else {
                throw new Error(response.data.message || 'Profil bilgileri alınamadı');
            }
        } catch (error: any) {
            console.error('❌ getProfileInfo sırasında hata oluştu:', error);
            throw error;
        }
    },

    // Update Profile
    async updateProfile(profileData: UpdateProfileRequest): Promise<any> {
        try {
            const response = await apiClient.post(API_ENDPOINTS.UPDATE_PROFILE, profileData);
            return response.data;
        } catch (error: any) {
            console.error('❌ Update profile error:', error);
            throw error;
        }
    },

    // Update Profile Picture
    async updateProfilePicture(imageFile: File): Promise<any> {
        console.log('📤 Update profile picture request');

        try {
            const formData = new FormData();
            formData.append('file', imageFile);

            const response = await apiClient.put(API_ENDPOINTS.PROFILE_PICTURE, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });
            console.log('✅ Update profile picture response:', response.data);
            return response.data;
        } catch (error: any) {
            console.error('❌ Update profile picture error:', error);
            throw error;
        }
    },

    // Delete Profile Picture
    async deleteProfilePicture(): Promise<any> {
        console.log('🗑️ Delete profile picture request');

        try {
            const response = await apiClient.delete(API_ENDPOINTS.DELETE_PROFILE_PICTURE);
            console.log('✅ Delete profile picture response:', response.data);
            return response.data;
        } catch (error: any) {
            console.error('❌ Delete profile picture error:', error);
            throw error;
        }
    },
}; 