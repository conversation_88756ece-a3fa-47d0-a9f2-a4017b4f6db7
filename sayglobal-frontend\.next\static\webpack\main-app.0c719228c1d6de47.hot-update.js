"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("main-app",{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/client/app-find-source-map-url.js ***!
  \******************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"findSourceMapURL\", ({\n    enumerable: true,\n    get: function() {\n        return findSourceMapURL;\n    }\n}));\nconst basePath =  false || '';\nconst pathname = \"\" + basePath + \"/__nextjs_source-map\";\nconst findSourceMapURL =  true ? function findSourceMapURL(filename) {\n    if (filename === '') {\n        return null;\n    }\n    if (filename.startsWith(document.location.origin) && filename.includes('/_next/static')) {\n        // This is a request for a client chunk. This can only happen when\n        // using Turbopack. In this case, since we control how those source\n        // maps are generated, we can safely assume that the sourceMappingURL\n        // is relative to the filename, with an added `.map` extension. The\n        // browser can just request this file, and it gets served through the\n        // normal dev server, without the need to route this through\n        // the `/__nextjs_source-map` dev middleware.\n        return \"\" + filename + \".map\";\n    }\n    const url = new URL(pathname, document.location.origin);\n    url.searchParams.set('filename', filename);\n    return url.href;\n} : 0;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-find-source-map-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2FwcC1maW5kLXNvdXJjZS1tYXAtdXJsLmpzIiwibWFwcGluZ3MiOiI7Ozs7b0RBR2FBOzs7ZUFBQUE7OztBQUhiLE1BQU1DLFdBQVdDLE1BQWtDLElBQUk7QUFDdkQsTUFBTUcsV0FBWSxLQUFFSixXQUFTO0FBRXRCLE1BQU1ELG1CQUNYRSxLQUFvQixHQUNoQixTQUFTRixpQkFBaUJPLFFBQWdCO0lBQ3hDLElBQUlBLGFBQWEsSUFBSTtRQUNuQixPQUFPO0lBQ1Q7SUFFQSxJQUNFQSxTQUFTQyxVQUFVLENBQUNDLFNBQVNDLFFBQVEsQ0FBQ0MsTUFBTSxLQUM1Q0osU0FBU0ssUUFBUSxDQUFDLGtCQUNsQjtRQUNBLGtFQUFrRTtRQUNsRSxtRUFBbUU7UUFDbkUscUVBQXFFO1FBQ3JFLG1FQUFtRTtRQUNuRSxxRUFBcUU7UUFDckUsNERBQTREO1FBQzVELDZDQUE2QztRQUM3QyxPQUFRLEtBQUVMLFdBQVM7SUFDckI7SUFFQSxNQUFNTSxNQUFNLElBQUlDLElBQUlULFVBQVVJLFNBQVNDLFFBQVEsQ0FBQ0MsTUFBTTtJQUN0REUsSUFBSUUsWUFBWSxDQUFDQyxHQUFHLENBQUMsWUFBWVQ7SUFFakMsT0FBT00sSUFBSUksSUFBSTtBQUNqQixJQUNBQyxDQUFTQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBU1VTXFxEZXNrdG9wXFxzcmNcXGNsaWVudFxcYXBwLWZpbmQtc291cmNlLW1hcC11cmwudHMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgYmFzZVBhdGggPSBwcm9jZXNzLmVudi5fX05FWFRfUk9VVEVSX0JBU0VQQVRIIHx8ICcnXG5jb25zdCBwYXRobmFtZSA9IGAke2Jhc2VQYXRofS9fX25leHRqc19zb3VyY2UtbWFwYFxuXG5leHBvcnQgY29uc3QgZmluZFNvdXJjZU1hcFVSTCA9XG4gIHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnXG4gICAgPyBmdW5jdGlvbiBmaW5kU291cmNlTWFwVVJMKGZpbGVuYW1lOiBzdHJpbmcpOiBzdHJpbmcgfCBudWxsIHtcbiAgICAgICAgaWYgKGZpbGVuYW1lID09PSAnJykge1xuICAgICAgICAgIHJldHVybiBudWxsXG4gICAgICAgIH1cblxuICAgICAgICBpZiAoXG4gICAgICAgICAgZmlsZW5hbWUuc3RhcnRzV2l0aChkb2N1bWVudC5sb2NhdGlvbi5vcmlnaW4pICYmXG4gICAgICAgICAgZmlsZW5hbWUuaW5jbHVkZXMoJy9fbmV4dC9zdGF0aWMnKVxuICAgICAgICApIHtcbiAgICAgICAgICAvLyBUaGlzIGlzIGEgcmVxdWVzdCBmb3IgYSBjbGllbnQgY2h1bmsuIFRoaXMgY2FuIG9ubHkgaGFwcGVuIHdoZW5cbiAgICAgICAgICAvLyB1c2luZyBUdXJib3BhY2suIEluIHRoaXMgY2FzZSwgc2luY2Ugd2UgY29udHJvbCBob3cgdGhvc2Ugc291cmNlXG4gICAgICAgICAgLy8gbWFwcyBhcmUgZ2VuZXJhdGVkLCB3ZSBjYW4gc2FmZWx5IGFzc3VtZSB0aGF0IHRoZSBzb3VyY2VNYXBwaW5nVVJMXG4gICAgICAgICAgLy8gaXMgcmVsYXRpdmUgdG8gdGhlIGZpbGVuYW1lLCB3aXRoIGFuIGFkZGVkIGAubWFwYCBleHRlbnNpb24uIFRoZVxuICAgICAgICAgIC8vIGJyb3dzZXIgY2FuIGp1c3QgcmVxdWVzdCB0aGlzIGZpbGUsIGFuZCBpdCBnZXRzIHNlcnZlZCB0aHJvdWdoIHRoZVxuICAgICAgICAgIC8vIG5vcm1hbCBkZXYgc2VydmVyLCB3aXRob3V0IHRoZSBuZWVkIHRvIHJvdXRlIHRoaXMgdGhyb3VnaFxuICAgICAgICAgIC8vIHRoZSBgL19fbmV4dGpzX3NvdXJjZS1tYXBgIGRldiBtaWRkbGV3YXJlLlxuICAgICAgICAgIHJldHVybiBgJHtmaWxlbmFtZX0ubWFwYFxuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgdXJsID0gbmV3IFVSTChwYXRobmFtZSwgZG9jdW1lbnQubG9jYXRpb24ub3JpZ2luKVxuICAgICAgICB1cmwuc2VhcmNoUGFyYW1zLnNldCgnZmlsZW5hbWUnLCBmaWxlbmFtZSlcblxuICAgICAgICByZXR1cm4gdXJsLmhyZWZcbiAgICAgIH1cbiAgICA6IHVuZGVmaW5lZFxuIl0sIm5hbWVzIjpbImZpbmRTb3VyY2VNYXBVUkwiLCJiYXNlUGF0aCIsInByb2Nlc3MiLCJlbnYiLCJfX05FWFRfUk9VVEVSX0JBU0VQQVRIIiwicGF0aG5hbWUiLCJOT0RFX0VOViIsImZpbGVuYW1lIiwic3RhcnRzV2l0aCIsImRvY3VtZW50IiwibG9jYXRpb24iLCJvcmlnaW4iLCJpbmNsdWRlcyIsInVybCIsIlVSTCIsInNlYXJjaFBhcmFtcyIsInNldCIsImhyZWYiLCJ1bmRlZmluZWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/app-router-instance.js ***!
  \*************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createMutableActionQueue: function() {\n        return createMutableActionQueue;\n    },\n    dispatchNavigateAction: function() {\n        return dispatchNavigateAction;\n    },\n    dispatchTraverseAction: function() {\n        return dispatchTraverseAction;\n    },\n    getCurrentAppRouterState: function() {\n        return getCurrentAppRouterState;\n    },\n    publicAppRouterInstance: function() {\n        return publicAppRouterInstance;\n    }\n});\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _routerreducer = __webpack_require__(/*! ./router-reducer/router-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _isthenable = __webpack_require__(/*! ../../shared/lib/is-thenable */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/is-thenable.js\");\nconst _segmentcache = __webpack_require__(/*! ./segment-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/segment-cache.js\");\nconst _useactionqueue = __webpack_require__(/*! ./use-action-queue */ \"(app-pages-browser)/./node_modules/next/dist/client/components/use-action-queue.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _approuter = __webpack_require__(/*! ./app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./router-reducer/reducers/prefetch-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\nconst _links = __webpack_require__(/*! ./links */ \"(app-pages-browser)/./node_modules/next/dist/client/components/links.js\");\nfunction runRemainingActions(actionQueue, setState) {\n    if (actionQueue.pending !== null) {\n        actionQueue.pending = actionQueue.pending.next;\n        if (actionQueue.pending !== null) {\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n            runAction({\n                actionQueue,\n                action: actionQueue.pending,\n                setState\n            });\n        } else {\n            // No more actions are pending, check if a refresh is needed\n            if (actionQueue.needsRefresh) {\n                actionQueue.needsRefresh = false;\n                actionQueue.dispatch({\n                    type: _routerreducertypes.ACTION_REFRESH,\n                    origin: window.location.origin\n                }, setState);\n            }\n        }\n    }\n}\nasync function runAction(param) {\n    let { actionQueue, action, setState } = param;\n    const prevState = actionQueue.state;\n    actionQueue.pending = action;\n    const payload = action.payload;\n    const actionResult = actionQueue.action(prevState, payload);\n    function handleResult(nextState) {\n        // if we discarded this action, the state should also be discarded\n        if (action.discarded) {\n            return;\n        }\n        actionQueue.state = nextState;\n        runRemainingActions(actionQueue, setState);\n        action.resolve(nextState);\n    }\n    // if the action is a promise, set up a callback to resolve it\n    if ((0, _isthenable.isThenable)(actionResult)) {\n        actionResult.then(handleResult, (err)=>{\n            runRemainingActions(actionQueue, setState);\n            action.reject(err);\n        });\n    } else {\n        handleResult(actionResult);\n    }\n}\nfunction dispatchAction(actionQueue, payload, setState) {\n    let resolvers = {\n        resolve: setState,\n        reject: ()=>{}\n    };\n    // most of the action types are async with the exception of restore\n    // it's important that restore is handled quickly since it's fired on the popstate event\n    // and we don't want to add any delay on a back/forward nav\n    // this only creates a promise for the async actions\n    if (payload.type !== _routerreducertypes.ACTION_RESTORE) {\n        // Create the promise and assign the resolvers to the object.\n        const deferredPromise = new Promise((resolve, reject)=>{\n            resolvers = {\n                resolve,\n                reject\n            };\n        });\n        (0, _react.startTransition)(()=>{\n            // we immediately notify React of the pending promise -- the resolver is attached to the action node\n            // and will be called when the associated action promise resolves\n            setState(deferredPromise);\n        });\n    }\n    const newAction = {\n        payload,\n        next: null,\n        resolve: resolvers.resolve,\n        reject: resolvers.reject\n    };\n    // Check if the queue is empty\n    if (actionQueue.pending === null) {\n        // The queue is empty, so add the action and start it immediately\n        // Mark this action as the last in the queue\n        actionQueue.last = newAction;\n        runAction({\n            actionQueue,\n            action: newAction,\n            setState\n        });\n    } else if (payload.type === _routerreducertypes.ACTION_NAVIGATE || payload.type === _routerreducertypes.ACTION_RESTORE) {\n        // Navigations (including back/forward) take priority over any pending actions.\n        // Mark the pending action as discarded (so the state is never applied) and start the navigation action immediately.\n        actionQueue.pending.discarded = true;\n        // The rest of the current queue should still execute after this navigation.\n        // (Note that it can't contain any earlier navigations, because we always put those into `actionQueue.pending` by calling `runAction`)\n        newAction.next = actionQueue.pending.next;\n        // if the pending action was a server action, mark the queue as needing a refresh once events are processed\n        if (actionQueue.pending.payload.type === _routerreducertypes.ACTION_SERVER_ACTION) {\n            actionQueue.needsRefresh = true;\n        }\n        runAction({\n            actionQueue,\n            action: newAction,\n            setState\n        });\n    } else {\n        // The queue is not empty, so add the action to the end of the queue\n        // It will be started by runRemainingActions after the previous action finishes\n        if (actionQueue.last !== null) {\n            actionQueue.last.next = newAction;\n        }\n        actionQueue.last = newAction;\n    }\n}\nlet globalActionQueue = null;\nfunction createMutableActionQueue(initialState, instrumentationHooks) {\n    const actionQueue = {\n        state: initialState,\n        dispatch: (payload, setState)=>dispatchAction(actionQueue, payload, setState),\n        action: async (state, action)=>{\n            const result = (0, _routerreducer.reducer)(state, action);\n            return result;\n        },\n        pending: null,\n        last: null,\n        onRouterTransitionStart: instrumentationHooks !== null && typeof instrumentationHooks.onRouterTransitionStart === 'function' ? instrumentationHooks.onRouterTransitionStart : null\n    };\n    if (true) {\n        // The action queue is lazily created on hydration, but after that point\n        // it doesn't change. So we can store it in a global rather than pass\n        // it around everywhere via props/context.\n        if (globalActionQueue !== null) {\n            throw Object.defineProperty(new Error('Internal Next.js Error: createMutableActionQueue was called more ' + 'than once'), \"__NEXT_ERROR_CODE\", {\n                value: \"E624\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        globalActionQueue = actionQueue;\n    }\n    return actionQueue;\n}\nfunction getCurrentAppRouterState() {\n    return globalActionQueue !== null ? globalActionQueue.state : null;\n}\nfunction getAppRouterActionQueue() {\n    if (globalActionQueue === null) {\n        throw Object.defineProperty(new Error('Internal Next.js error: Router action dispatched before initialization.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E668\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    return globalActionQueue;\n}\nfunction getProfilingHookForOnNavigationStart() {\n    if (globalActionQueue !== null) {\n        return globalActionQueue.onRouterTransitionStart;\n    }\n    return null;\n}\nfunction dispatchNavigateAction(href, navigateType, shouldScroll, linkInstanceRef) {\n    // TODO: This stuff could just go into the reducer. Leaving as-is for now\n    // since we're about to rewrite all the router reducer stuff anyway.\n    const url = new URL((0, _addbasepath.addBasePath)(href), location.href);\n    if (false) {}\n    (0, _links.setLinkForCurrentNavigation)(linkInstanceRef);\n    const onRouterTransitionStart = getProfilingHookForOnNavigationStart();\n    if (onRouterTransitionStart !== null) {\n        onRouterTransitionStart(href, navigateType);\n    }\n    (0, _useactionqueue.dispatchAppRouterAction)({\n        type: _routerreducertypes.ACTION_NAVIGATE,\n        url,\n        isExternalUrl: (0, _approuter.isExternalURL)(url),\n        locationSearch: location.search,\n        shouldScroll,\n        navigateType,\n        allowAliasing: true\n    });\n}\nfunction dispatchTraverseAction(href, tree) {\n    const onRouterTransitionStart = getProfilingHookForOnNavigationStart();\n    if (onRouterTransitionStart !== null) {\n        onRouterTransitionStart(href, 'traverse');\n    }\n    (0, _useactionqueue.dispatchAppRouterAction)({\n        type: _routerreducertypes.ACTION_RESTORE,\n        url: new URL(href),\n        tree\n    });\n}\nconst publicAppRouterInstance = {\n    back: ()=>window.history.back(),\n    forward: ()=>window.history.forward(),\n    prefetch:  false ? // cache. So we don't need to dispatch an action.\n    0 : (href, options)=>{\n        // Use the old prefetch implementation.\n        const actionQueue = getAppRouterActionQueue();\n        const url = (0, _approuter.createPrefetchURL)(href);\n        if (url !== null) {\n            var _options_kind;\n            // The prefetch reducer doesn't actually update any state or\n            // trigger a rerender. It just writes to a mutable cache. So we\n            // shouldn't bother calling setState/dispatch; we can just re-run\n            // the reducer directly using the current state.\n            // TODO: Refactor this away from a \"reducer\" so it's\n            // less confusing.\n            (0, _prefetchreducer.prefetchReducer)(actionQueue.state, {\n                type: _routerreducertypes.ACTION_PREFETCH,\n                url,\n                kind: (_options_kind = options == null ? void 0 : options.kind) != null ? _options_kind : _routerreducertypes.PrefetchKind.FULL\n            });\n        }\n    },\n    replace: (href, options)=>{\n        (0, _react.startTransition)(()=>{\n            var _options_scroll;\n            dispatchNavigateAction(href, 'replace', (_options_scroll = options == null ? void 0 : options.scroll) != null ? _options_scroll : true, null);\n        });\n    },\n    push: (href, options)=>{\n        (0, _react.startTransition)(()=>{\n            var _options_scroll;\n            dispatchNavigateAction(href, 'push', (_options_scroll = options == null ? void 0 : options.scroll) != null ? _options_scroll : true, null);\n        });\n    },\n    refresh: ()=>{\n        (0, _react.startTransition)(()=>{\n            (0, _useactionqueue.dispatchAppRouterAction)({\n                type: _routerreducertypes.ACTION_REFRESH,\n                origin: window.location.origin\n            });\n        });\n    },\n    hmrRefresh: ()=>{\n        if (false) {} else {\n            (0, _react.startTransition)(()=>{\n                (0, _useactionqueue.dispatchAppRouterAction)({\n                    type: _routerreducertypes.ACTION_HMR_REFRESH,\n                    origin: window.location.origin\n                });\n            });\n        }\n    }\n};\n// Exists for debugging purposes. Don't use in application code.\nif ( true && window.next) {\n    window.next.router = publicAppRouterInstance;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router-instance.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/client/components/app-router.js ***!
  \****************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createEmptyCacheNode: function() {\n        return createEmptyCacheNode;\n    },\n    createPrefetchURL: function() {\n        return createPrefetchURL;\n    },\n    default: function() {\n        return AppRouter;\n    },\n    isExternalURL: function() {\n        return isExternalURL;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _createhreffromurl = __webpack_require__(/*! ./router-reducer/create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _hooksclientcontextsharedruntime = __webpack_require__(/*! ../../shared/lib/hooks-client-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\");\nconst _useactionqueue = __webpack_require__(/*! ./use-action-queue */ \"(app-pages-browser)/./node_modules/next/dist/client/components/use-action-queue.js\");\nconst _errorboundary = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\"));\nconst _isbot = __webpack_require__(/*! ../../shared/lib/router/utils/is-bot */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-bot.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _approuterannouncer = __webpack_require__(/*! ./app-router-announcer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-announcer.js\");\nconst _redirectboundary = __webpack_require__(/*! ./redirect-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js\");\nconst _findheadincache = __webpack_require__(/*! ./router-reducer/reducers/find-head-in-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/find-head-in-cache.js\");\nconst _unresolvedthenable = __webpack_require__(/*! ./unresolved-thenable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/unresolved-thenable.js\");\nconst _removebasepath = __webpack_require__(/*! ../remove-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/remove-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ../has-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/has-base-path.js\");\nconst _computechangedpath = __webpack_require__(/*! ./router-reducer/compute-changed-path */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\");\nconst _navfailurehandler = __webpack_require__(/*! ./nav-failure-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/nav-failure-handler.js\");\nconst _approuterinstance = __webpack_require__(/*! ./app-router-instance */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\");\nconst _redirect = __webpack_require__(/*! ./redirect */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect.js\");\nconst _redirecterror = __webpack_require__(/*! ./redirect-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-error.js\");\nconst _links = __webpack_require__(/*! ./links */ \"(app-pages-browser)/./node_modules/next/dist/client/components/links.js\");\nconst globalMutable = {};\nfunction isExternalURL(url) {\n    return url.origin !== window.location.origin;\n}\nfunction createPrefetchURL(href) {\n    // Don't prefetch for bots as they don't navigate.\n    if ((0, _isbot.isBot)(window.navigator.userAgent)) {\n        return null;\n    }\n    let url;\n    try {\n        url = new URL((0, _addbasepath.addBasePath)(href), window.location.href);\n    } catch (_) {\n        // TODO: Does this need to throw or can we just console.error instead? Does\n        // anyone rely on this throwing? (Seems unlikely.)\n        throw Object.defineProperty(new Error(\"Cannot prefetch '\" + href + \"' because it cannot be converted to a URL.\"), \"__NEXT_ERROR_CODE\", {\n            value: \"E234\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    // Don't prefetch during development (improves compilation performance)\n    if (true) {\n        return null;\n    }\n    // External urls can't be prefetched in the same way.\n    if (isExternalURL(url)) {\n        return null;\n    }\n    return url;\n}\nfunction HistoryUpdater(param) {\n    let { appRouterState } = param;\n    (0, _react.useInsertionEffect)(()=>{\n        if (false) {}\n        const { tree, pushRef, canonicalUrl } = appRouterState;\n        const historyState = {\n            ...pushRef.preserveCustomHistoryState ? window.history.state : {},\n            // Identifier is shortened intentionally.\n            // __NA is used to identify if the history entry can be handled by the app-router.\n            // __N is used to identify if the history entry can be handled by the old router.\n            __NA: true,\n            __PRIVATE_NEXTJS_INTERNALS_TREE: tree\n        };\n        if (pushRef.pendingPush && // Skip pushing an additional history entry if the canonicalUrl is the same as the current url.\n        // This mirrors the browser behavior for normal navigation.\n        (0, _createhreffromurl.createHrefFromUrl)(new URL(window.location.href)) !== canonicalUrl) {\n            // This intentionally mutates React state, pushRef is overwritten to ensure additional push/replace calls do not trigger an additional history entry.\n            pushRef.pendingPush = false;\n            window.history.pushState(historyState, '', canonicalUrl);\n        } else {\n            window.history.replaceState(historyState, '', canonicalUrl);\n        }\n    }, [\n        appRouterState\n    ]);\n    (0, _react.useEffect)(()=>{\n        // The Next-Url and the base tree may affect the result of a prefetch\n        // task. Re-prefetch all visible links with the updated values. In most\n        // cases, this will not result in any new network requests, only if\n        // the prefetch result actually varies on one of these inputs.\n        if (false) {}\n    }, [\n        appRouterState.nextUrl,\n        appRouterState.tree\n    ]);\n    return null;\n}\n_c = HistoryUpdater;\nfunction createEmptyCacheNode() {\n    return {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt: -1\n    };\n}\nfunction copyNextJsInternalHistoryState(data) {\n    if (data == null) data = {};\n    const currentState = window.history.state;\n    const __NA = currentState == null ? void 0 : currentState.__NA;\n    if (__NA) {\n        data.__NA = __NA;\n    }\n    const __PRIVATE_NEXTJS_INTERNALS_TREE = currentState == null ? void 0 : currentState.__PRIVATE_NEXTJS_INTERNALS_TREE;\n    if (__PRIVATE_NEXTJS_INTERNALS_TREE) {\n        data.__PRIVATE_NEXTJS_INTERNALS_TREE = __PRIVATE_NEXTJS_INTERNALS_TREE;\n    }\n    return data;\n}\nfunction Head(param) {\n    let { headCacheNode } = param;\n    // If this segment has a `prefetchHead`, it's the statically prefetched data.\n    // We should use that on initial render instead of `head`. Then we'll switch\n    // to `head` when the dynamic response streams in.\n    const head = headCacheNode !== null ? headCacheNode.head : null;\n    const prefetchHead = headCacheNode !== null ? headCacheNode.prefetchHead : null;\n    // If no prefetch data is available, then we go straight to rendering `head`.\n    const resolvedPrefetchRsc = prefetchHead !== null ? prefetchHead : head;\n    // We use `useDeferredValue` to handle switching between the prefetched and\n    // final values. The second argument is returned on initial render, then it\n    // re-renders with the first argument.\n    return (0, _react.useDeferredValue)(head, resolvedPrefetchRsc);\n}\n_c1 = Head;\n/**\n * The global router that wraps the application components.\n */ function Router(param) {\n    let { actionQueue, assetPrefix, globalError } = param;\n    const state = (0, _useactionqueue.useActionQueue)(actionQueue);\n    const { canonicalUrl } = state;\n    // Add memoized pathname/query for useSearchParams and usePathname.\n    const { searchParams, pathname } = (0, _react.useMemo)(()=>{\n        const url = new URL(canonicalUrl,  false ? 0 : window.location.href);\n        return {\n            // This is turned into a readonly class in `useSearchParams`\n            searchParams: url.searchParams,\n            pathname: (0, _hasbasepath.hasBasePath)(url.pathname) ? (0, _removebasepath.removeBasePath)(url.pathname) : url.pathname\n        };\n    }, [\n        canonicalUrl\n    ]);\n    if (true) {\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const { cache, prefetchCache, tree } = state;\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0, _react.useEffect)(()=>{\n            // Add `window.nd` for debugging purposes.\n            // This is not meant for use in applications as concurrent rendering will affect the cache/tree/router.\n            // @ts-ignore this is for debugging\n            window.nd = {\n                router: _approuterinstance.publicAppRouterInstance,\n                cache,\n                prefetchCache,\n                tree\n            };\n        }, [\n            cache,\n            prefetchCache,\n            tree\n        ]);\n    }\n    (0, _react.useEffect)(()=>{\n        // If the app is restored from bfcache, it's possible that\n        // pushRef.mpaNavigation is true, which would mean that any re-render of this component\n        // would trigger the mpa navigation logic again from the lines below.\n        // This will restore the router to the initial state in the event that the app is restored from bfcache.\n        function handlePageShow(event) {\n            var _window_history_state;\n            if (!event.persisted || !((_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE)) {\n                return;\n            }\n            // Clear the pendingMpaPath value so that a subsequent MPA navigation to the same URL can be triggered.\n            // This is necessary because if the browser restored from bfcache, the pendingMpaPath would still be set to the value\n            // of the last MPA navigation.\n            globalMutable.pendingMpaPath = undefined;\n            (0, _useactionqueue.dispatchAppRouterAction)({\n                type: _routerreducertypes.ACTION_RESTORE,\n                url: new URL(window.location.href),\n                tree: window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE\n            });\n        }\n        window.addEventListener('pageshow', handlePageShow);\n        return ()=>{\n            window.removeEventListener('pageshow', handlePageShow);\n        };\n    }, []);\n    (0, _react.useEffect)(()=>{\n        // Ensure that any redirect errors that bubble up outside of the RedirectBoundary\n        // are caught and handled by the router.\n        function handleUnhandledRedirect(event) {\n            const error = 'reason' in event ? event.reason : event.error;\n            if ((0, _redirecterror.isRedirectError)(error)) {\n                event.preventDefault();\n                const url = (0, _redirect.getURLFromRedirectError)(error);\n                const redirectType = (0, _redirect.getRedirectTypeFromError)(error);\n                // TODO: This should access the router methods directly, rather than\n                // go through the public interface.\n                if (redirectType === _redirecterror.RedirectType.push) {\n                    _approuterinstance.publicAppRouterInstance.push(url, {});\n                } else {\n                    _approuterinstance.publicAppRouterInstance.replace(url, {});\n                }\n            }\n        }\n        window.addEventListener('error', handleUnhandledRedirect);\n        window.addEventListener('unhandledrejection', handleUnhandledRedirect);\n        return ()=>{\n            window.removeEventListener('error', handleUnhandledRedirect);\n            window.removeEventListener('unhandledrejection', handleUnhandledRedirect);\n        };\n    }, []);\n    // When mpaNavigation flag is set do a hard navigation to the new url.\n    // Infinitely suspend because we don't actually want to rerender any child\n    // components with the new URL and any entangled state updates shouldn't\n    // commit either (eg: useTransition isPending should stay true until the page\n    // unloads).\n    //\n    // This is a side effect in render. Don't try this at home, kids. It's\n    // probably safe because we know this is a singleton component and it's never\n    // in <Offscreen>. At least I hope so. (It will run twice in dev strict mode,\n    // but that's... fine?)\n    const { pushRef } = state;\n    if (pushRef.mpaNavigation) {\n        // if there's a re-render, we don't want to trigger another redirect if one is already in flight to the same URL\n        if (globalMutable.pendingMpaPath !== canonicalUrl) {\n            const location = window.location;\n            if (pushRef.pendingPush) {\n                location.assign(canonicalUrl);\n            } else {\n                location.replace(canonicalUrl);\n            }\n            globalMutable.pendingMpaPath = canonicalUrl;\n        }\n        // TODO-APP: Should we listen to navigateerror here to catch failed\n        // navigations somehow? And should we call window.stop() if a SPA navigation\n        // should interrupt an MPA one?\n        (0, _react.use)(_unresolvedthenable.unresolvedThenable);\n    }\n    (0, _react.useEffect)(()=>{\n        const originalPushState = window.history.pushState.bind(window.history);\n        const originalReplaceState = window.history.replaceState.bind(window.history);\n        // Ensure the canonical URL in the Next.js Router is updated when the URL is changed so that `usePathname` and `useSearchParams` hold the pushed values.\n        const applyUrlFromHistoryPushReplace = (url)=>{\n            var _window_history_state;\n            const href = window.location.href;\n            const tree = (_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE;\n            (0, _react.startTransition)(()=>{\n                (0, _useactionqueue.dispatchAppRouterAction)({\n                    type: _routerreducertypes.ACTION_RESTORE,\n                    url: new URL(url != null ? url : href, href),\n                    tree\n                });\n            });\n        };\n        /**\n     * Patch pushState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.pushState = function pushState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalPushState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalPushState(data, _unused, url);\n        };\n        /**\n     * Patch replaceState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.replaceState = function replaceState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalReplaceState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalReplaceState(data, _unused, url);\n        };\n        /**\n     * Handle popstate event, this is used to handle back/forward in the browser.\n     * By default dispatches ACTION_RESTORE, however if the history entry was not pushed/replaced by app-router it will reload the page.\n     * That case can happen when the old router injected the history entry.\n     */ const onPopState = (event)=>{\n            if (!event.state) {\n                // TODO-APP: this case only happens when pushState/replaceState was called outside of Next.js. It should probably reload the page in this case.\n                return;\n            }\n            // This case happens when the history entry was pushed by the `pages` router.\n            if (!event.state.__NA) {\n                window.location.reload();\n                return;\n            }\n            // TODO-APP: Ideally the back button should not use startTransition as it should apply the updates synchronously\n            // Without startTransition works if the cache is there for this path\n            (0, _react.startTransition)(()=>{\n                (0, _approuterinstance.dispatchTraverseAction)(window.location.href, event.state.__PRIVATE_NEXTJS_INTERNALS_TREE);\n            });\n        };\n        // Register popstate event to call onPopstate.\n        window.addEventListener('popstate', onPopState);\n        return ()=>{\n            window.history.pushState = originalPushState;\n            window.history.replaceState = originalReplaceState;\n            window.removeEventListener('popstate', onPopState);\n        };\n    }, []);\n    const { cache, tree, nextUrl, focusAndScrollRef } = state;\n    const matchingHead = (0, _react.useMemo)(()=>{\n        return (0, _findheadincache.findHeadInCache)(cache, tree[1]);\n    }, [\n        cache,\n        tree\n    ]);\n    // Add memoized pathParams for useParams.\n    const pathParams = (0, _react.useMemo)(()=>{\n        return (0, _computechangedpath.getSelectedParams)(tree);\n    }, [\n        tree\n    ]);\n    const layoutRouterContext = (0, _react.useMemo)(()=>{\n        return {\n            parentTree: tree,\n            parentCacheNode: cache,\n            parentSegmentPath: null,\n            // Root node always has `url`\n            // Provided in AppTreeContext to ensure it can be overwritten in layout-router\n            url: canonicalUrl\n        };\n    }, [\n        tree,\n        cache,\n        canonicalUrl\n    ]);\n    const globalLayoutRouterContext = (0, _react.useMemo)(()=>{\n        return {\n            tree,\n            focusAndScrollRef,\n            nextUrl\n        };\n    }, [\n        tree,\n        focusAndScrollRef,\n        nextUrl\n    ]);\n    let head;\n    if (matchingHead !== null) {\n        // The head is wrapped in an extra component so we can use\n        // `useDeferredValue` to swap between the prefetched and final versions of\n        // the head. (This is what LayoutRouter does for segment data, too.)\n        //\n        // The `key` is used to remount the component whenever the head moves to\n        // a different segment.\n        const [headCacheNode, headKey] = matchingHead;\n        head = /*#__PURE__*/ (0, _jsxruntime.jsx)(Head, {\n            headCacheNode: headCacheNode\n        }, headKey);\n    } else {\n        head = null;\n    }\n    let content = /*#__PURE__*/ (0, _jsxruntime.jsxs)(_redirectboundary.RedirectBoundary, {\n        children: [\n            head,\n            cache.rsc,\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_approuterannouncer.AppRouterAnnouncer, {\n                tree: tree\n            })\n        ]\n    });\n    if (true) {\n        // In development, we apply few error boundaries and hot-reloader:\n        // - DevRootHTTPAccessFallbackBoundary: avoid using navigation API like notFound() in root layout\n        // - HotReloader:\n        //  - hot-reload the app when the code changes\n        //  - render dev overlay\n        //  - catch runtime errors and display global-error when necessary\n        if (true) {\n            const { DevRootHTTPAccessFallbackBoundary } = __webpack_require__(/*! ./dev-root-http-access-fallback-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js\");\n            content = /*#__PURE__*/ (0, _jsxruntime.jsx)(DevRootHTTPAccessFallbackBoundary, {\n                children: content\n            });\n        }\n        const HotReloader = (__webpack_require__(/*! ./react-dev-overlay/app/hot-reloader-client */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js\")[\"default\"]);\n        content = /*#__PURE__*/ (0, _jsxruntime.jsx)(HotReloader, {\n            assetPrefix: assetPrefix,\n            globalError: globalError,\n            children: content\n        });\n    } else {}\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(HistoryUpdater, {\n                appRouterState: state\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(RuntimeStyles, {}),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathParamsContext.Provider, {\n                value: pathParams,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathnameContext.Provider, {\n                    value: pathname,\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.SearchParamsContext.Provider, {\n                        value: searchParams,\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.GlobalLayoutRouterContext.Provider, {\n                            value: globalLayoutRouterContext,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.AppRouterContext.Provider, {\n                                value: _approuterinstance.publicAppRouterInstance,\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.LayoutRouterContext.Provider, {\n                                    value: layoutRouterContext,\n                                    children: content\n                                })\n                            })\n                        })\n                    })\n                })\n            })\n        ]\n    });\n}\n_c2 = Router;\nfunction AppRouter(param) {\n    let { actionQueue, globalErrorComponentAndStyles: [globalErrorComponent, globalErrorStyles], assetPrefix } = param;\n    (0, _navfailurehandler.useNavFailureHandler)();\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorboundary.ErrorBoundary, {\n        // At the very top level, use the default GlobalError component as the final fallback.\n        // When the app router itself fails, which means the framework itself fails, we show the default error.\n        errorComponent: _errorboundary.default,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Router, {\n            actionQueue: actionQueue,\n            assetPrefix: assetPrefix,\n            globalError: [\n                globalErrorComponent,\n                globalErrorStyles\n            ]\n        })\n    });\n}\n_c3 = AppRouter;\nconst runtimeStyles = new Set();\nlet runtimeStyleChanged = new Set();\nglobalThis._N_E_STYLE_LOAD = function(href) {\n    let len = runtimeStyles.size;\n    runtimeStyles.add(href);\n    if (runtimeStyles.size !== len) {\n        runtimeStyleChanged.forEach((cb)=>cb());\n    }\n    // TODO figure out how to get a promise here\n    // But maybe it's not necessary as react would block rendering until it's loaded\n    return Promise.resolve();\n};\nfunction RuntimeStyles() {\n    _s();\n    const [, forceUpdate] = _react.default.useState(0);\n    const renderedStylesSize = runtimeStyles.size;\n    (0, _react.useEffect)(()=>{\n        const changed = ()=>forceUpdate((c)=>c + 1);\n        runtimeStyleChanged.add(changed);\n        if (renderedStylesSize !== runtimeStyles.size) {\n            changed();\n        }\n        return ()=>{\n            runtimeStyleChanged.delete(changed);\n        };\n    }, [\n        renderedStylesSize,\n        forceUpdate\n    ]);\n    const dplId =  false ? 0 : '';\n    return [\n        ...runtimeStyles\n    ].map((href, i)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n            rel: \"stylesheet\",\n            href: \"\" + href + dplId,\n            // @ts-ignore\n            precedence: \"next\"\n        }, i));\n}\n_s(RuntimeStyles, \"Eht7Kgdrrgt5B4LSklQ7qDPo8Aw=\");\n_c4 = RuntimeStyles;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router.js.map\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HistoryUpdater\");\n$RefreshReg$(_c1, \"Head\");\n$RefreshReg$(_c2, \"Router\");\n$RefreshReg$(_c3, \"AppRouter\");\n$RefreshReg$(_c4, \"RuntimeStyles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/globals/intercept-console-error.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/globals/intercept-console-error.js ***!
  \*************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    originConsoleError: function() {\n        return originConsoleError;\n    },\n    patchConsoleError: function() {\n        return patchConsoleError;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../lib/is-error */ \"(app-pages-browser)/./node_modules/next/dist/lib/is-error.js\"));\nconst _isnextroutererror = __webpack_require__(/*! ../is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../errors/use-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js\");\nconst _console = __webpack_require__(/*! ../../lib/console */ \"(app-pages-browser)/./node_modules/next/dist/client/lib/console.js\");\nconst originConsoleError = globalThis.console.error;\nfunction patchConsoleError() {\n    // Ensure it's only patched once\n    if (false) {}\n    window.console.error = function error() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        let maybeError;\n        if (true) {\n            const { error: replayedError } = (0, _console.parseConsoleArgs)(args);\n            if (replayedError) {\n                maybeError = replayedError;\n            } else if ((0, _iserror.default)(args[0])) {\n                maybeError = args[0];\n            } else {\n                // See https://github.com/facebook/react/blob/d50323eb845c5fde0d720cae888bf35dedd05506/packages/react-reconciler/src/ReactFiberErrorLogger.js#L78\n                maybeError = args[1];\n            }\n        } else {}\n        if (!(0, _isnextroutererror.isNextRouterError)(maybeError)) {\n            if (true) {\n                (0, _useerrorhandler.handleConsoleError)(// but if we pass the error directly, `handleClientError` will ignore it\n                maybeError, args);\n            }\n            originConsoleError.apply(window.console, args);\n        }\n    };\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=intercept-console-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/globals/intercept-console-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"HTTPAccessFallbackBoundary\", ({\n    enumerable: true,\n    get: function() {\n        return HTTPAccessFallbackBoundary;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _navigationuntracked = __webpack_require__(/*! ../navigation-untracked */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation-untracked.js\");\nconst _httpaccessfallback = __webpack_require__(/*! ./http-access-fallback */ \"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/http-access-fallback.js\");\nconst _warnonce = __webpack_require__(/*! ../../../shared/lib/utils/warn-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nclass HTTPAccessFallbackErrorBoundary extends _react.default.Component {\n    componentDidCatch() {\n        if ( true && this.props.missingSlots && this.props.missingSlots.size > 0 && // A missing children slot is the typical not-found case, so no need to warn\n        !this.props.missingSlots.has('children')) {\n            let warningMessage = 'No default component was found for a parallel route rendered on this page. Falling back to nearest NotFound boundary.\\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/routing/parallel-routes#defaultjs\\n\\n';\n            const formattedSlots = Array.from(this.props.missingSlots).sort((a, b)=>a.localeCompare(b)).map((slot)=>\"@\" + slot).join(', ');\n            warningMessage += 'Missing slots: ' + formattedSlots;\n            (0, _warnonce.warnOnce)(warningMessage);\n        }\n    }\n    static getDerivedStateFromError(error) {\n        if ((0, _httpaccessfallback.isHTTPAccessFallbackError)(error)) {\n            const httpStatus = (0, _httpaccessfallback.getAccessFallbackHTTPStatus)(error);\n            return {\n                triggeredStatus: httpStatus\n            };\n        }\n        // Re-throw if error is not for 404\n        throw error;\n    }\n    static getDerivedStateFromProps(props, state) {\n        /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */ if (props.pathname !== state.previousPathname && state.triggeredStatus) {\n            return {\n                triggeredStatus: undefined,\n                previousPathname: props.pathname\n            };\n        }\n        return {\n            triggeredStatus: state.triggeredStatus,\n            previousPathname: props.pathname\n        };\n    }\n    render() {\n        const { notFound, forbidden, unauthorized, children } = this.props;\n        const { triggeredStatus } = this.state;\n        const errorComponents = {\n            [_httpaccessfallback.HTTPAccessErrorStatus.NOT_FOUND]: notFound,\n            [_httpaccessfallback.HTTPAccessErrorStatus.FORBIDDEN]: forbidden,\n            [_httpaccessfallback.HTTPAccessErrorStatus.UNAUTHORIZED]: unauthorized\n        };\n        if (triggeredStatus) {\n            const isNotFound = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.NOT_FOUND && notFound;\n            const isForbidden = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.FORBIDDEN && forbidden;\n            const isUnauthorized = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.UNAUTHORIZED && unauthorized;\n            // If there's no matched boundary in this layer, keep throwing the error by rendering the children\n            if (!(isNotFound || isForbidden || isUnauthorized)) {\n                return children;\n            }\n            return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                        name: \"robots\",\n                        content: \"noindex\"\n                    }),\n                     true && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                        name: \"boundary-next-error\",\n                        content: (0, _httpaccessfallback.getAccessFallbackErrorTypeByStatus)(triggeredStatus)\n                    }),\n                    errorComponents[triggeredStatus]\n                ]\n            });\n        }\n        return children;\n    }\n    constructor(props){\n        super(props);\n        this.state = {\n            triggeredStatus: undefined,\n            previousPathname: props.pathname\n        };\n    }\n}\nfunction HTTPAccessFallbackBoundary(param) {\n    let { notFound, forbidden, unauthorized, children } = param;\n    // When we're rendering the missing params shell, this will return null. This\n    // is because we won't be rendering any not found boundaries or error\n    // boundaries for the missing params shell. When this runs on the client\n    // (where these error can occur), we will get the correct pathname.\n    const pathname = (0, _navigationuntracked.useUntrackedPathname)();\n    const missingSlots = (0, _react.useContext)(_approutercontextsharedruntime.MissingSlotContext);\n    const hasErrorFallback = !!(notFound || forbidden || unauthorized);\n    if (hasErrorFallback) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(HTTPAccessFallbackErrorBoundary, {\n            pathname: pathname,\n            notFound: notFound,\n            forbidden: forbidden,\n            unauthorized: unauthorized,\n            missingSlots: missingSlots,\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: children\n    });\n}\n_c = HTTPAccessFallbackBoundary;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-boundary.js.map\nvar _c;\n$RefreshReg$(_c, \"HTTPAccessFallbackBoundary\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js ***!
  \*******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"AppDevOverlay\", ({\n    enumerable: true,\n    get: function() {\n        return AppDevOverlay;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _appdevoverlayerrorboundary = __webpack_require__(/*! ./app-dev-overlay-error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay-error-boundary.js\");\nconst _fontstyles = __webpack_require__(/*! ../font/font-styles */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/font/font-styles.js\");\nconst _devoverlay = __webpack_require__(/*! ../ui/dev-overlay */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/dev-overlay.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../../errors/use-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js\");\nconst _isnextroutererror = __webpack_require__(/*! ../../is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _constants = __webpack_require__(/*! ../../../../shared/lib/errors/constants */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/errors/constants.js\");\nfunction readSsrError() {\n    if (typeof document === 'undefined') {\n        return null;\n    }\n    const ssrErrorTemplateTag = document.querySelector('template[data-next-error-message]');\n    if (ssrErrorTemplateTag) {\n        const message = ssrErrorTemplateTag.getAttribute('data-next-error-message');\n        const stack = ssrErrorTemplateTag.getAttribute('data-next-error-stack');\n        const digest = ssrErrorTemplateTag.getAttribute('data-next-error-digest');\n        const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n        if (digest) {\n            ;\n            error.digest = digest;\n        }\n        // Skip Next.js SSR'd internal errors that which will be handled by the error boundaries.\n        if ((0, _isnextroutererror.isNextRouterError)(error)) {\n            return null;\n        }\n        error.stack = stack || '';\n        return error;\n    }\n    return null;\n}\n// Needs to be in the same error boundary as the shell.\n// If it commits, we know we recovered from an SSR error.\n// If it doesn't commit, we errored again and React will take care of error reporting.\nfunction ReplaySsrOnlyErrors(param) {\n    let { onBlockingError } = param;\n    if (true) {\n        // Need to read during render. The attributes will be gone after commit.\n        const ssrError = readSsrError();\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0, _react.useEffect)(()=>{\n            if (ssrError !== null) {\n                // TODO(veil): Produces wrong Owner Stack\n                // TODO(veil): Mark as recoverable error\n                // TODO(veil): console.error\n                (0, _useerrorhandler.handleClientError)(ssrError);\n                // If it's missing root tags, we can't recover, make it blocking.\n                if (ssrError.digest === _constants.MISSING_ROOT_TAGS_ERROR) {\n                    onBlockingError();\n                }\n            }\n        }, [\n            ssrError,\n            onBlockingError\n        ]);\n    }\n    return null;\n}\n_c = ReplaySsrOnlyErrors;\nfunction AppDevOverlay(param) {\n    let { state, globalError, children } = param;\n    const [isErrorOverlayOpen, setIsErrorOverlayOpen] = (0, _react.useState)(false);\n    const openOverlay = (0, _react.useCallback)(()=>{\n        setIsErrorOverlayOpen(true);\n    }, []);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(_appdevoverlayerrorboundary.AppDevOverlayErrorBoundary, {\n                globalError: globalError,\n                onError: setIsErrorOverlayOpen,\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(ReplaySsrOnlyErrors, {\n                        onBlockingError: openOverlay\n                    }),\n                    children\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_fontstyles.FontStyles, {}),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_devoverlay.DevOverlay, {\n                        state: state,\n                        isErrorOverlayOpen: isErrorOverlayOpen,\n                        setIsErrorOverlayOpen: setIsErrorOverlayOpen\n                    })\n                ]\n            })\n        ]\n    });\n}\n_c1 = AppDevOverlay;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-dev-overlay.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"ReplaySsrOnlyErrors\");\n$RefreshReg$(_c1, \"AppDevOverlay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVhY3QtZGV2LW92ZXJsYXkvYXBwL2FwcC1kZXYtb3ZlcmxheS5qcyIsIm1hcHBpbmdzIjoiOzs7O2lEQXNFZ0JBOzs7ZUFBQUE7Ozs7bUNBbkVpQzt3REFDTjt3Q0FDaEI7d0NBQ0E7NkNBQ087K0NBQ0E7dUNBQ007QUFFeEMsU0FBU0M7SUFDUCxJQUFJLE9BQU9DLGFBQWEsYUFBYTtRQUNuQyxPQUFPO0lBQ1Q7SUFFQSxNQUFNQyxzQkFBc0JELFNBQVNFLGFBQWEsQ0FDaEQ7SUFFRixJQUFJRCxxQkFBcUI7UUFDdkIsTUFBTUUsVUFBa0JGLG9CQUFvQkcsWUFBWSxDQUN0RDtRQUVGLE1BQU1DLFFBQVFKLG9CQUFvQkcsWUFBWSxDQUFDO1FBQy9DLE1BQU1FLFNBQVNMLG9CQUFvQkcsWUFBWSxDQUFDO1FBQ2hELE1BQU1HLFFBQVEscUJBQWtCLENBQWxCLElBQUlDLE1BQU1MLFVBQVY7bUJBQUE7d0JBQUE7MEJBQUE7UUFBaUI7UUFDL0IsSUFBSUcsUUFBUTs7WUFDUkMsTUFBY0QsTUFBTSxHQUFHQTtRQUMzQjtRQUNBLHlGQUF5RjtRQUN6RixJQUFJRyxDQUFBQSxHQUFBQSxtQkFBQUEsaUJBQUFBLEVBQWtCRixRQUFRO1lBQzVCLE9BQU87UUFDVDtRQUNBQSxNQUFNRixLQUFLLEdBQUdBLFNBQVM7UUFDdkIsT0FBT0U7SUFDVDtJQUVBLE9BQU87QUFDVDtBQUVBLHVEQUF1RDtBQUN2RCx5REFBeUQ7QUFDekQsc0ZBQXNGO0FBQ3RGLDZCQUE2QixLQUk1QjtJQUo0QixNQUMzQkksZUFBZSxFQUdoQixHQUo0QjtJQUszQixJQUFJQyxJQUFvQixFQUFtQjtRQUN6Qyx3RUFBd0U7UUFDeEUsTUFBTUcsV0FBV2hCO1FBQ2pCLHNEQUFzRDtRQUN0RGlCLENBQUFBLEdBQUFBLE9BQUFBLFNBQUFBLEVBQVU7WUFDUixJQUFJRCxhQUFhLE1BQU07Z0JBQ3JCLHlDQUF5QztnQkFDekMsd0NBQXdDO2dCQUN4Qyw0QkFBNEI7Z0JBQzVCRSxDQUFBQSxHQUFBQSxpQkFBQUEsaUJBQUFBLEVBQWtCRjtnQkFFbEIsaUVBQWlFO2dCQUNqRSxJQUFJQSxTQUFTVCxNQUFNLEtBQUtZLFdBQUFBLHVCQUF1QixFQUFFO29CQUMvQ1A7Z0JBQ0Y7WUFDRjtRQUNGLEdBQUc7WUFBQ0k7WUFBVUo7U0FBZ0I7SUFDaEM7SUFFQSxPQUFPO0FBQ1Q7S0F6QlNEO0FBMkJGLHVCQUF1QixLQVE3QjtJQVI2QixNQUM1QlMsS0FBSyxFQUNMQyxXQUFXLEVBQ1hDLFFBQVEsRUFLVCxHQVI2QjtJQVM1QixNQUFNLENBQUNDLG9CQUFvQkMsc0JBQXNCLEdBQUdDLENBQUFBLEdBQUFBLE9BQUFBLFFBQUFBLEVBQVM7SUFDN0QsTUFBTUMsY0FBY0MsQ0FBQUEsR0FBQUEsT0FBQUEsV0FBQUEsRUFBWTtRQUM5Qkgsc0JBQXNCO0lBQ3hCLEdBQUcsRUFBRTtJQUVMLHFCQUNFOzswQkFDRSxzQkFBQ0ksNEJBQUFBLDBCQUEwQjtnQkFDekJQLGFBQWFBO2dCQUNiUSxTQUFTTDs7a0NBRVQscUJBQUNiLHFCQUFBQTt3QkFBb0JDLGlCQUFpQmM7O29CQUNyQ0o7OzswQkFFSDs7a0NBRUUscUJBQUNRLFlBQUFBLFVBQVU7a0NBQ1gscUJBQUNDLFlBQUFBLFVBQVU7d0JBQ1RYLE9BQU9BO3dCQUNQRyxvQkFBb0JBO3dCQUNwQkMsdUJBQXVCQTs7Ozs7O0FBS2pDO01BbENnQnpCIiwic291cmNlcyI6WyJDOlxcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXHJlYWN0LWRldi1vdmVybGF5XFxhcHBcXGFwcC1kZXYtb3ZlcmxheS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBPdmVybGF5U3RhdGUgfSBmcm9tICcuLi9zaGFyZWQnXG5pbXBvcnQgdHlwZSB7IEdsb2JhbEVycm9yQ29tcG9uZW50IH0gZnJvbSAnLi4vLi4vZXJyb3ItYm91bmRhcnknXG5cbmltcG9ydCB7IHVzZUNhbGxiYWNrLCB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBBcHBEZXZPdmVybGF5RXJyb3JCb3VuZGFyeSB9IGZyb20gJy4vYXBwLWRldi1vdmVybGF5LWVycm9yLWJvdW5kYXJ5J1xuaW1wb3J0IHsgRm9udFN0eWxlcyB9IGZyb20gJy4uL2ZvbnQvZm9udC1zdHlsZXMnXG5pbXBvcnQgeyBEZXZPdmVybGF5IH0gZnJvbSAnLi4vdWkvZGV2LW92ZXJsYXknXG5pbXBvcnQgeyBoYW5kbGVDbGllbnRFcnJvciB9IGZyb20gJy4uLy4uL2Vycm9ycy91c2UtZXJyb3ItaGFuZGxlcidcbmltcG9ydCB7IGlzTmV4dFJvdXRlckVycm9yIH0gZnJvbSAnLi4vLi4vaXMtbmV4dC1yb3V0ZXItZXJyb3InXG5pbXBvcnQgeyBNSVNTSU5HX1JPT1RfVEFHU19FUlJPUiB9IGZyb20gJy4uLy4uLy4uLy4uL3NoYXJlZC9saWIvZXJyb3JzL2NvbnN0YW50cydcblxuZnVuY3Rpb24gcmVhZFNzckVycm9yKCk6IChFcnJvciAmIHsgZGlnZXN0Pzogc3RyaW5nIH0pIHwgbnVsbCB7XG4gIGlmICh0eXBlb2YgZG9jdW1lbnQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgcmV0dXJuIG51bGxcbiAgfVxuXG4gIGNvbnN0IHNzckVycm9yVGVtcGxhdGVUYWcgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKFxuICAgICd0ZW1wbGF0ZVtkYXRhLW5leHQtZXJyb3ItbWVzc2FnZV0nXG4gIClcbiAgaWYgKHNzckVycm9yVGVtcGxhdGVUYWcpIHtcbiAgICBjb25zdCBtZXNzYWdlOiBzdHJpbmcgPSBzc3JFcnJvclRlbXBsYXRlVGFnLmdldEF0dHJpYnV0ZShcbiAgICAgICdkYXRhLW5leHQtZXJyb3ItbWVzc2FnZSdcbiAgICApIVxuICAgIGNvbnN0IHN0YWNrID0gc3NyRXJyb3JUZW1wbGF0ZVRhZy5nZXRBdHRyaWJ1dGUoJ2RhdGEtbmV4dC1lcnJvci1zdGFjaycpXG4gICAgY29uc3QgZGlnZXN0ID0gc3NyRXJyb3JUZW1wbGF0ZVRhZy5nZXRBdHRyaWJ1dGUoJ2RhdGEtbmV4dC1lcnJvci1kaWdlc3QnKVxuICAgIGNvbnN0IGVycm9yID0gbmV3IEVycm9yKG1lc3NhZ2UpXG4gICAgaWYgKGRpZ2VzdCkge1xuICAgICAgOyhlcnJvciBhcyBhbnkpLmRpZ2VzdCA9IGRpZ2VzdFxuICAgIH1cbiAgICAvLyBTa2lwIE5leHQuanMgU1NSJ2QgaW50ZXJuYWwgZXJyb3JzIHRoYXQgd2hpY2ggd2lsbCBiZSBoYW5kbGVkIGJ5IHRoZSBlcnJvciBib3VuZGFyaWVzLlxuICAgIGlmIChpc05leHRSb3V0ZXJFcnJvcihlcnJvcikpIHtcbiAgICAgIHJldHVybiBudWxsXG4gICAgfVxuICAgIGVycm9yLnN0YWNrID0gc3RhY2sgfHwgJydcbiAgICByZXR1cm4gZXJyb3JcbiAgfVxuXG4gIHJldHVybiBudWxsXG59XG5cbi8vIE5lZWRzIHRvIGJlIGluIHRoZSBzYW1lIGVycm9yIGJvdW5kYXJ5IGFzIHRoZSBzaGVsbC5cbi8vIElmIGl0IGNvbW1pdHMsIHdlIGtub3cgd2UgcmVjb3ZlcmVkIGZyb20gYW4gU1NSIGVycm9yLlxuLy8gSWYgaXQgZG9lc24ndCBjb21taXQsIHdlIGVycm9yZWQgYWdhaW4gYW5kIFJlYWN0IHdpbGwgdGFrZSBjYXJlIG9mIGVycm9yIHJlcG9ydGluZy5cbmZ1bmN0aW9uIFJlcGxheVNzck9ubHlFcnJvcnMoe1xuICBvbkJsb2NraW5nRXJyb3IsXG59OiB7XG4gIG9uQmxvY2tpbmdFcnJvcjogKCkgPT4gdm9pZFxufSkge1xuICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICAgIC8vIE5lZWQgdG8gcmVhZCBkdXJpbmcgcmVuZGVyLiBUaGUgYXR0cmlidXRlcyB3aWxsIGJlIGdvbmUgYWZ0ZXIgY29tbWl0LlxuICAgIGNvbnN0IHNzckVycm9yID0gcmVhZFNzckVycm9yKClcbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaG9va3MvcnVsZXMtb2YtaG9va3NcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgaWYgKHNzckVycm9yICE9PSBudWxsKSB7XG4gICAgICAgIC8vIFRPRE8odmVpbCk6IFByb2R1Y2VzIHdyb25nIE93bmVyIFN0YWNrXG4gICAgICAgIC8vIFRPRE8odmVpbCk6IE1hcmsgYXMgcmVjb3ZlcmFibGUgZXJyb3JcbiAgICAgICAgLy8gVE9ETyh2ZWlsKTogY29uc29sZS5lcnJvclxuICAgICAgICBoYW5kbGVDbGllbnRFcnJvcihzc3JFcnJvcilcblxuICAgICAgICAvLyBJZiBpdCdzIG1pc3Npbmcgcm9vdCB0YWdzLCB3ZSBjYW4ndCByZWNvdmVyLCBtYWtlIGl0IGJsb2NraW5nLlxuICAgICAgICBpZiAoc3NyRXJyb3IuZGlnZXN0ID09PSBNSVNTSU5HX1JPT1RfVEFHU19FUlJPUikge1xuICAgICAgICAgIG9uQmxvY2tpbmdFcnJvcigpXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9LCBbc3NyRXJyb3IsIG9uQmxvY2tpbmdFcnJvcl0pXG4gIH1cblxuICByZXR1cm4gbnVsbFxufVxuXG5leHBvcnQgZnVuY3Rpb24gQXBwRGV2T3ZlcmxheSh7XG4gIHN0YXRlLFxuICBnbG9iYWxFcnJvcixcbiAgY2hpbGRyZW4sXG59OiB7XG4gIHN0YXRlOiBPdmVybGF5U3RhdGVcbiAgZ2xvYmFsRXJyb3I6IFtHbG9iYWxFcnJvckNvbXBvbmVudCwgUmVhY3QuUmVhY3ROb2RlXVxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIGNvbnN0IFtpc0Vycm9yT3ZlcmxheU9wZW4sIHNldElzRXJyb3JPdmVybGF5T3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3Qgb3Blbk92ZXJsYXkgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgc2V0SXNFcnJvck92ZXJsYXlPcGVuKHRydWUpXG4gIH0sIFtdKVxuXG4gIHJldHVybiAoXG4gICAgPD5cbiAgICAgIDxBcHBEZXZPdmVybGF5RXJyb3JCb3VuZGFyeVxuICAgICAgICBnbG9iYWxFcnJvcj17Z2xvYmFsRXJyb3J9XG4gICAgICAgIG9uRXJyb3I9e3NldElzRXJyb3JPdmVybGF5T3Blbn1cbiAgICAgID5cbiAgICAgICAgPFJlcGxheVNzck9ubHlFcnJvcnMgb25CbG9ja2luZ0Vycm9yPXtvcGVuT3ZlcmxheX0gLz5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9BcHBEZXZPdmVybGF5RXJyb3JCb3VuZGFyeT5cbiAgICAgIDw+XG4gICAgICAgIHsvKiBGb250cyBjYW4gb25seSBiZSBsb2FkZWQgb3V0c2lkZSB0aGUgU2hhZG93IERPTS4gKi99XG4gICAgICAgIDxGb250U3R5bGVzIC8+XG4gICAgICAgIDxEZXZPdmVybGF5XG4gICAgICAgICAgc3RhdGU9e3N0YXRlfVxuICAgICAgICAgIGlzRXJyb3JPdmVybGF5T3Blbj17aXNFcnJvck92ZXJsYXlPcGVufVxuICAgICAgICAgIHNldElzRXJyb3JPdmVybGF5T3Blbj17c2V0SXNFcnJvck92ZXJsYXlPcGVufVxuICAgICAgICAvPlxuICAgICAgPC8+XG4gICAgPC8+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJBcHBEZXZPdmVybGF5IiwicmVhZFNzckVycm9yIiwiZG9jdW1lbnQiLCJzc3JFcnJvclRlbXBsYXRlVGFnIiwicXVlcnlTZWxlY3RvciIsIm1lc3NhZ2UiLCJnZXRBdHRyaWJ1dGUiLCJzdGFjayIsImRpZ2VzdCIsImVycm9yIiwiRXJyb3IiLCJpc05leHRSb3V0ZXJFcnJvciIsIlJlcGxheVNzck9ubHlFcnJvcnMiLCJvbkJsb2NraW5nRXJyb3IiLCJwcm9jZXNzIiwiZW52IiwiTk9ERV9FTlYiLCJzc3JFcnJvciIsInVzZUVmZmVjdCIsImhhbmRsZUNsaWVudEVycm9yIiwiTUlTU0lOR19ST09UX1RBR1NfRVJST1IiLCJzdGF0ZSIsImdsb2JhbEVycm9yIiwiY2hpbGRyZW4iLCJpc0Vycm9yT3ZlcmxheU9wZW4iLCJzZXRJc0Vycm9yT3ZlcmxheU9wZW4iLCJ1c2VTdGF0ZSIsIm9wZW5PdmVybGF5IiwidXNlQ2FsbGJhY2siLCJBcHBEZXZPdmVybGF5RXJyb3JCb3VuZGFyeSIsIm9uRXJyb3IiLCJGb250U3R5bGVzIiwiRGV2T3ZlcmxheSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js ***!
  \************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createInitialRouterState\", ({\n    enumerable: true,\n    get: function() {\n        return createInitialRouterState;\n    }\n}));\nconst _createhreffromurl = __webpack_require__(/*! ./create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _filllazyitemstillleafwithhead = __webpack_require__(/*! ./fill-lazy-items-till-leaf-with-head */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fill-lazy-items-till-leaf-with-head.js\");\nconst _computechangedpath = __webpack_require__(/*! ./compute-changed-path */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\");\nconst _prefetchcacheutils = __webpack_require__(/*! ./prefetch-cache-utils */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _refetchinactiveparallelsegments = __webpack_require__(/*! ./refetch-inactive-parallel-segments */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/refetch-inactive-parallel-segments.js\");\nconst _flightdatahelpers = __webpack_require__(/*! ../../flight-data-helpers */ \"(app-pages-browser)/./node_modules/next/dist/client/flight-data-helpers.js\");\nfunction createInitialRouterState(param) {\n    let { navigatedAt, initialFlightData, initialCanonicalUrlParts, initialParallelRoutes, location, couldBeIntercepted, postponed, prerendered } = param;\n    // When initialized on the server, the canonical URL is provided as an array of parts.\n    // This is to ensure that when the RSC payload streamed to the client, crawlers don't interpret it\n    // as a URL that should be crawled.\n    const initialCanonicalUrl = initialCanonicalUrlParts.join('/');\n    const normalizedFlightData = (0, _flightdatahelpers.getFlightDataPartsFromPath)(initialFlightData[0]);\n    const { tree: initialTree, seedData: initialSeedData, head: initialHead } = normalizedFlightData;\n    // For the SSR render, seed data should always be available (we only send back a `null` response\n    // in the case of a `loading` segment, pre-PPR.)\n    const rsc = initialSeedData == null ? void 0 : initialSeedData[1];\n    var _initialSeedData_;\n    const loading = (_initialSeedData_ = initialSeedData == null ? void 0 : initialSeedData[3]) != null ? _initialSeedData_ : null;\n    const cache = {\n        lazyData: null,\n        rsc,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        // The cache gets seeded during the first render. `initialParallelRoutes` ensures the cache from the first render is there during the second render.\n        parallelRoutes: initialParallelRoutes,\n        loading,\n        navigatedAt\n    };\n    const canonicalUrl = // This is safe to do as canonicalUrl can't be rendered, it's only used to control the history updates in the useEffect further down in this file.\n    location ? (0, _createhreffromurl.createHrefFromUrl)(location) : initialCanonicalUrl;\n    (0, _refetchinactiveparallelsegments.addRefreshMarkerToActiveParallelSegments)(initialTree, canonicalUrl);\n    const prefetchCache = new Map();\n    // When the cache hasn't been seeded yet we fill the cache with the head.\n    if (initialParallelRoutes === null || initialParallelRoutes.size === 0) {\n        (0, _filllazyitemstillleafwithhead.fillLazyItemsTillLeafWithHead)(navigatedAt, cache, undefined, initialTree, initialSeedData, initialHead, undefined);\n    }\n    var _ref;\n    const initialState = {\n        tree: initialTree,\n        cache,\n        prefetchCache,\n        pushRef: {\n            pendingPush: false,\n            mpaNavigation: false,\n            // First render needs to preserve the previous window.history.state\n            // to avoid it being overwritten on navigation back/forward with MPA Navigation.\n            preserveCustomHistoryState: true\n        },\n        focusAndScrollRef: {\n            apply: false,\n            onlyHashChange: false,\n            hashFragment: null,\n            segmentPaths: []\n        },\n        canonicalUrl,\n        nextUrl: (_ref = (0, _computechangedpath.extractPathFromFlightRouterState)(initialTree) || (location == null ? void 0 : location.pathname)) != null ? _ref : null\n    };\n    if (false) {}\n    return initialState;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=create-initial-router-state.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DYNAMIC_STALETIME_MS: function() {\n        return DYNAMIC_STALETIME_MS;\n    },\n    STATIC_STALETIME_MS: function() {\n        return STATIC_STALETIME_MS;\n    },\n    createSeededPrefetchCacheEntry: function() {\n        return createSeededPrefetchCacheEntry;\n    },\n    getOrCreatePrefetchCacheEntry: function() {\n        return getOrCreatePrefetchCacheEntry;\n    },\n    prunePrefetchCache: function() {\n        return prunePrefetchCache;\n    }\n});\nconst _fetchserverresponse = __webpack_require__(/*! ./fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./reducers/prefetch-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\nconst INTERCEPTION_CACHE_KEY_MARKER = '%';\n/**\n * Creates a cache key for the router prefetch cache\n *\n * @param url - The URL being navigated to\n * @param nextUrl - an internal URL, primarily used for handling rewrites. Defaults to '/'.\n * @return The generated prefetch cache key.\n */ function createPrefetchCacheKeyImpl(url, includeSearchParams, prefix) {\n    // Initially we only use the pathname as the cache key. We don't want to include\n    // search params so that multiple URLs with the same search parameter can re-use\n    // loading states.\n    let pathnameFromUrl = url.pathname;\n    // RSC responses can differ based on search params, specifically in the case where we aren't\n    // returning a partial response (ie with `PrefetchKind.AUTO`).\n    // In the auto case, since loading.js & layout.js won't have access to search params,\n    // we can safely re-use that cache entry. But for full prefetches, we should not\n    // re-use the cache entry as the response may differ.\n    if (includeSearchParams) {\n        // if we have a full prefetch, we can include the search param in the key,\n        // as we'll be getting back a full response. The server might have read the search\n        // params when generating the full response.\n        pathnameFromUrl += url.search;\n    }\n    if (prefix) {\n        return \"\" + prefix + INTERCEPTION_CACHE_KEY_MARKER + pathnameFromUrl;\n    }\n    return pathnameFromUrl;\n}\nfunction createPrefetchCacheKey(url, kind, nextUrl) {\n    return createPrefetchCacheKeyImpl(url, kind === _routerreducertypes.PrefetchKind.FULL, nextUrl);\n}\nfunction getExistingCacheEntry(url, kind, nextUrl, prefetchCache, allowAliasing) {\n    if (kind === void 0) kind = _routerreducertypes.PrefetchKind.TEMPORARY;\n    // We first check if there's a more specific interception route prefetch entry\n    // This is because when we detect a prefetch that corresponds with an interception route, we prefix it with nextUrl (see `createPrefetchCacheKey`)\n    // to avoid conflicts with other pages that may have the same URL but render different things depending on the `Next-URL` header.\n    for (const maybeNextUrl of [\n        nextUrl,\n        null\n    ]){\n        const cacheKeyWithParams = createPrefetchCacheKeyImpl(url, true, maybeNextUrl);\n        const cacheKeyWithoutParams = createPrefetchCacheKeyImpl(url, false, maybeNextUrl);\n        // First, we check if we have a cache entry that exactly matches the URL\n        const cacheKeyToUse = url.search ? cacheKeyWithParams : cacheKeyWithoutParams;\n        const existingEntry = prefetchCache.get(cacheKeyToUse);\n        if (existingEntry && allowAliasing) {\n            // We know we're returning an aliased entry when the pathname matches but the search params don't,\n            const isAliased = existingEntry.url.pathname === url.pathname && existingEntry.url.search !== url.search;\n            if (isAliased) {\n                return {\n                    ...existingEntry,\n                    aliased: true\n                };\n            }\n            return existingEntry;\n        }\n        // If the request contains search params, and we're not doing a full prefetch, we can return the\n        // param-less entry if it exists.\n        // This is technically covered by the check at the bottom of this function, which iterates over cache entries,\n        // but lets us arrive there quicker in the param-full case.\n        const entryWithoutParams = prefetchCache.get(cacheKeyWithoutParams);\n        if (false) {}\n    }\n    // If we've gotten to this point, we didn't find a specific cache entry that matched\n    // the request URL.\n    // We attempt a partial match by checking if there's a cache entry with the same pathname.\n    // Regardless of what we find, since it doesn't correspond with the requested URL, we'll mark it \"aliased\".\n    // This will signal to the router that it should only apply the loading state on the prefetched data.\n    if (false) {}\n    return undefined;\n}\nfunction getOrCreatePrefetchCacheEntry(param) {\n    let { url, nextUrl, tree, prefetchCache, kind, allowAliasing = true } = param;\n    const existingCacheEntry = getExistingCacheEntry(url, kind, nextUrl, prefetchCache, allowAliasing);\n    if (existingCacheEntry) {\n        // Grab the latest status of the cache entry and update it\n        existingCacheEntry.status = getPrefetchEntryCacheStatus(existingCacheEntry);\n        // when `kind` is provided, an explicit prefetch was requested.\n        // if the requested prefetch is \"full\" and the current cache entry wasn't, we want to re-prefetch with the new intent\n        const switchedToFullPrefetch = existingCacheEntry.kind !== _routerreducertypes.PrefetchKind.FULL && kind === _routerreducertypes.PrefetchKind.FULL;\n        if (switchedToFullPrefetch) {\n            // If we switched to a full prefetch, validate that the existing cache entry contained partial data.\n            // It's possible that the cache entry was seeded with full data but has a cache type of \"auto\" (ie when cache entries\n            // are seeded but without a prefetch intent)\n            existingCacheEntry.data.then((prefetchResponse)=>{\n                const isFullPrefetch = Array.isArray(prefetchResponse.flightData) && prefetchResponse.flightData.some((flightData)=>{\n                    // If we started rendering from the root and we returned RSC data (seedData), we already had a full prefetch.\n                    return flightData.isRootRender && flightData.seedData !== null;\n                });\n                if (!isFullPrefetch) {\n                    return createLazyPrefetchEntry({\n                        tree,\n                        url,\n                        nextUrl,\n                        prefetchCache,\n                        // If we didn't get an explicit prefetch kind, we want to set a temporary kind\n                        // rather than assuming the same intent as the previous entry, to be consistent with how we\n                        // lazily create prefetch entries when intent is left unspecified.\n                        kind: kind != null ? kind : _routerreducertypes.PrefetchKind.TEMPORARY\n                    });\n                }\n            });\n        }\n        // If the existing cache entry was marked as temporary, it means it was lazily created when attempting to get an entry,\n        // where we didn't have the prefetch intent. Now that we have the intent (in `kind`), we want to update the entry to the more accurate kind.\n        if (kind && existingCacheEntry.kind === _routerreducertypes.PrefetchKind.TEMPORARY) {\n            existingCacheEntry.kind = kind;\n        }\n        // We've determined that the existing entry we found is still valid, so we return it.\n        return existingCacheEntry;\n    }\n    // If we didn't return an entry, create a new one.\n    return createLazyPrefetchEntry({\n        tree,\n        url,\n        nextUrl,\n        prefetchCache,\n        kind: kind || _routerreducertypes.PrefetchKind.TEMPORARY\n    });\n}\n/*\n * Used to take an existing cache entry and prefix it with the nextUrl, if it exists.\n * This ensures that we don't have conflicting cache entries for the same URL (as is the case with route interception).\n */ function prefixExistingPrefetchCacheEntry(param) {\n    let { url, nextUrl, prefetchCache, existingCacheKey } = param;\n    const existingCacheEntry = prefetchCache.get(existingCacheKey);\n    if (!existingCacheEntry) {\n        // no-op -- there wasn't an entry to move\n        return;\n    }\n    const newCacheKey = createPrefetchCacheKey(url, existingCacheEntry.kind, nextUrl);\n    prefetchCache.set(newCacheKey, {\n        ...existingCacheEntry,\n        key: newCacheKey\n    });\n    prefetchCache.delete(existingCacheKey);\n    return newCacheKey;\n}\nfunction createSeededPrefetchCacheEntry(param) {\n    let { nextUrl, tree, prefetchCache, url, data, kind } = param;\n    // The initial cache entry technically includes full data, but it isn't explicitly prefetched -- we just seed the\n    // prefetch cache so that we can skip an extra prefetch request later, since we already have the data.\n    // if the prefetch corresponds with an interception route, we use the nextUrl to prefix the cache key\n    const prefetchCacheKey = data.couldBeIntercepted ? createPrefetchCacheKey(url, kind, nextUrl) : createPrefetchCacheKey(url, kind);\n    const prefetchEntry = {\n        treeAtTimeOfPrefetch: tree,\n        data: Promise.resolve(data),\n        kind,\n        prefetchTime: Date.now(),\n        lastUsedTime: Date.now(),\n        staleTime: -1,\n        key: prefetchCacheKey,\n        status: _routerreducertypes.PrefetchCacheEntryStatus.fresh,\n        url\n    };\n    prefetchCache.set(prefetchCacheKey, prefetchEntry);\n    return prefetchEntry;\n}\n/**\n * Creates a prefetch entry entry and enqueues a fetch request to retrieve the data.\n */ function createLazyPrefetchEntry(param) {\n    let { url, kind, tree, nextUrl, prefetchCache } = param;\n    const prefetchCacheKey = createPrefetchCacheKey(url, kind);\n    // initiates the fetch request for the prefetch and attaches a listener\n    // to the promise to update the prefetch cache entry when the promise resolves (if necessary)\n    const data = _prefetchreducer.prefetchQueue.enqueue(()=>(0, _fetchserverresponse.fetchServerResponse)(url, {\n            flightRouterState: tree,\n            nextUrl,\n            prefetchKind: kind\n        }).then((prefetchResponse)=>{\n            // TODO: `fetchServerResponse` should be more tighly coupled to these prefetch cache operations\n            // to avoid drift between this cache key prefixing logic\n            // (which is currently directly influenced by the server response)\n            let newCacheKey;\n            if (prefetchResponse.couldBeIntercepted) {\n                // Determine if we need to prefix the cache key with the nextUrl\n                newCacheKey = prefixExistingPrefetchCacheEntry({\n                    url,\n                    existingCacheKey: prefetchCacheKey,\n                    nextUrl,\n                    prefetchCache\n                });\n            }\n            // If the prefetch was a cache hit, we want to update the existing cache entry to reflect that it was a full prefetch.\n            // This is because we know that a static response will contain the full RSC payload, and can be updated to respect the `static`\n            // staleTime.\n            if (prefetchResponse.prerendered) {\n                const existingCacheEntry = prefetchCache.get(newCacheKey != null ? newCacheKey : prefetchCacheKey);\n                if (existingCacheEntry) {\n                    existingCacheEntry.kind = _routerreducertypes.PrefetchKind.FULL;\n                    if (prefetchResponse.staleTime !== -1) {\n                        // This is the stale time that was collected by the server during\n                        // static generation. Use this in place of the default stale time.\n                        existingCacheEntry.staleTime = prefetchResponse.staleTime;\n                    }\n                }\n            }\n            return prefetchResponse;\n        }));\n    const prefetchEntry = {\n        treeAtTimeOfPrefetch: tree,\n        data,\n        kind,\n        prefetchTime: Date.now(),\n        lastUsedTime: null,\n        staleTime: -1,\n        key: prefetchCacheKey,\n        status: _routerreducertypes.PrefetchCacheEntryStatus.fresh,\n        url\n    };\n    prefetchCache.set(prefetchCacheKey, prefetchEntry);\n    return prefetchEntry;\n}\nfunction prunePrefetchCache(prefetchCache) {\n    for (const [href, prefetchCacheEntry] of prefetchCache){\n        if (getPrefetchEntryCacheStatus(prefetchCacheEntry) === _routerreducertypes.PrefetchCacheEntryStatus.expired) {\n            prefetchCache.delete(href);\n        }\n    }\n}\nconst DYNAMIC_STALETIME_MS = Number(\"0\") * 1000;\nconst STATIC_STALETIME_MS = Number(\"300\") * 1000;\nfunction getPrefetchEntryCacheStatus(param) {\n    let { kind, prefetchTime, lastUsedTime, staleTime } = param;\n    if (staleTime !== -1) {\n        // `staleTime` is the value sent by the server during static generation.\n        // When this is available, it takes precedence over any of the heuristics\n        // that follow.\n        //\n        // TODO: When PPR is enabled, the server will *always* return a stale time\n        // when prefetching. We should never use a prefetch entry that hasn't yet\n        // received data from the server. So the only two cases should be 1) we use\n        // the server-generated stale time 2) the unresolved entry is discarded.\n        return Date.now() < prefetchTime + staleTime ? _routerreducertypes.PrefetchCacheEntryStatus.fresh : _routerreducertypes.PrefetchCacheEntryStatus.stale;\n    }\n    // We will re-use the cache entry data for up to the `dynamic` staletime window.\n    if (Date.now() < (lastUsedTime != null ? lastUsedTime : prefetchTime) + DYNAMIC_STALETIME_MS) {\n        return lastUsedTime ? _routerreducertypes.PrefetchCacheEntryStatus.reusable : _routerreducertypes.PrefetchCacheEntryStatus.fresh;\n    }\n    // For \"auto\" prefetching, we'll re-use only the loading boundary for up to `static` staletime window.\n    // A stale entry will only re-use the `loading` boundary, not the full data.\n    // This will trigger a \"lazy fetch\" for the full data.\n    if (kind === _routerreducertypes.PrefetchKind.AUTO) {\n        if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n            return _routerreducertypes.PrefetchCacheEntryStatus.stale;\n        }\n    }\n    // for \"full\" prefetching, we'll re-use the cache entry data for up to `static` staletime window.\n    if (kind === _routerreducertypes.PrefetchKind.FULL) {\n        if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n            return _routerreducertypes.PrefetchCacheEntryStatus.reusable;\n        }\n    }\n    return _routerreducertypes.PrefetchCacheEntryStatus.expired;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=prefetch-cache-utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js ***!
  \*************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hmrRefreshReducer\", ({\n    enumerable: true,\n    get: function() {\n        return hmrRefreshReducer;\n    }\n}));\nconst _fetchserverresponse = __webpack_require__(/*! ../fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _createhreffromurl = __webpack_require__(/*! ../create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _applyrouterstatepatchtotree = __webpack_require__(/*! ../apply-router-state-patch-to-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-router-state-patch-to-tree.js\");\nconst _isnavigatingtonewrootlayout = __webpack_require__(/*! ../is-navigating-to-new-root-layout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/is-navigating-to-new-root-layout.js\");\nconst _navigatereducer = __webpack_require__(/*! ./navigate-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js\");\nconst _handlemutable = __webpack_require__(/*! ../handle-mutable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-mutable.js\");\nconst _applyflightdata = __webpack_require__(/*! ../apply-flight-data */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-flight-data.js\");\nconst _approuter = __webpack_require__(/*! ../../app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _handlesegmentmismatch = __webpack_require__(/*! ../handle-segment-mismatch */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js\");\nconst _hasinterceptionrouteincurrenttree = __webpack_require__(/*! ./has-interception-route-in-current-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js\");\n// A version of refresh reducer that keeps the cache around instead of wiping all of it.\nfunction hmrRefreshReducerImpl(state, action) {\n    const { origin } = action;\n    const mutable = {};\n    const href = state.canonicalUrl;\n    mutable.preserveCustomHistoryState = false;\n    const cache = (0, _approuter.createEmptyCacheNode)();\n    // If the current tree was intercepted, the nextUrl should be included in the request.\n    // This is to ensure that the refresh request doesn't get intercepted, accidentally triggering the interception route.\n    const includeNextUrl = (0, _hasinterceptionrouteincurrenttree.hasInterceptionRouteInCurrentTree)(state.tree);\n    // TODO-APP: verify that `href` is not an external url.\n    // Fetch data from the root of the tree.\n    const navigatedAt = Date.now();\n    cache.lazyData = (0, _fetchserverresponse.fetchServerResponse)(new URL(href, origin), {\n        flightRouterState: [\n            state.tree[0],\n            state.tree[1],\n            state.tree[2],\n            'refetch'\n        ],\n        nextUrl: includeNextUrl ? state.nextUrl : null,\n        isHmrRefresh: true\n    });\n    return cache.lazyData.then((param)=>{\n        let { flightData, canonicalUrl: canonicalUrlOverride } = param;\n        // Handle case when navigating to page in `pages` from `app`\n        if (typeof flightData === 'string') {\n            return (0, _navigatereducer.handleExternalUrl)(state, mutable, flightData, state.pushRef.pendingPush);\n        }\n        // Remove cache.lazyData as it has been resolved at this point.\n        cache.lazyData = null;\n        let currentTree = state.tree;\n        let currentCache = state.cache;\n        for (const normalizedFlightData of flightData){\n            const { tree: treePatch, isRootRender } = normalizedFlightData;\n            if (!isRootRender) {\n                // TODO-APP: handle this case better\n                console.log('REFRESH FAILED');\n                return state;\n            }\n            const newTree = (0, _applyrouterstatepatchtotree.applyRouterStatePatchToTree)([\n                ''\n            ], currentTree, treePatch, state.canonicalUrl);\n            if (newTree === null) {\n                return (0, _handlesegmentmismatch.handleSegmentMismatch)(state, action, treePatch);\n            }\n            if ((0, _isnavigatingtonewrootlayout.isNavigatingToNewRootLayout)(currentTree, newTree)) {\n                return (0, _navigatereducer.handleExternalUrl)(state, mutable, href, state.pushRef.pendingPush);\n            }\n            const canonicalUrlOverrideHref = canonicalUrlOverride ? (0, _createhreffromurl.createHrefFromUrl)(canonicalUrlOverride) : undefined;\n            if (canonicalUrlOverride) {\n                mutable.canonicalUrl = canonicalUrlOverrideHref;\n            }\n            const applied = (0, _applyflightdata.applyFlightData)(navigatedAt, currentCache, cache, normalizedFlightData);\n            if (applied) {\n                mutable.cache = cache;\n                currentCache = cache;\n            }\n            mutable.patchedTree = newTree;\n            mutable.canonicalUrl = href;\n            currentTree = newTree;\n        }\n        return (0, _handlemutable.handleMutable)(state, mutable);\n    }, ()=>state);\n}\nfunction hmrRefreshReducerNoop(state, _action) {\n    return state;\n}\nconst hmrRefreshReducer =  false ? 0 : hmrRefreshReducerImpl;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hmr-refresh-reducer.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/use-action-queue.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next/dist/client/components/use-action-queue.js ***!
  \**********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    dispatchAppRouterAction: function() {\n        return dispatchAppRouterAction;\n    },\n    useActionQueue: function() {\n        return useActionQueue;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _isthenable = __webpack_require__(/*! ../../shared/lib/is-thenable */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/is-thenable.js\");\n// The app router state lives outside of React, so we can import the dispatch\n// method directly wherever we need it, rather than passing it around via props\n// or context.\nlet dispatch = null;\nfunction dispatchAppRouterAction(action) {\n    if (dispatch === null) {\n        throw Object.defineProperty(new Error('Internal Next.js error: Router action dispatched before initialization.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E668\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    dispatch(action);\n}\nfunction useActionQueue(actionQueue) {\n    _s();\n    const [state, setState] = _react.default.useState(actionQueue.state);\n    // Because of a known issue that requires to decode Flight streams inside the\n    // render phase, we have to be a bit clever and assign the dispatch method to\n    // a module-level variable upon initialization. The useState hook in this\n    // module only exists to synchronize state that lives outside of React.\n    // Ideally, what we'd do instead is pass the state as a prop to root.render;\n    // this is conceptually how we're modeling the app router state, despite the\n    // weird implementation details.\n    if (true) {\n        const useSyncDevRenderIndicator = (__webpack_require__(/*! ./react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator.js\").useSyncDevRenderIndicator);\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const syncDevRenderIndicator = useSyncDevRenderIndicator();\n        dispatch = (action)=>{\n            syncDevRenderIndicator(()=>{\n                actionQueue.dispatch(action, setState);\n            });\n        };\n    } else {}\n    return (0, _isthenable.isThenable)(state) ? (0, _react.use)(state) : state;\n}\n_s(useActionQueue, \"Rp0Tj1zyE8LTecjN/cjTzn46xPo=\");\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-action-queue.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/use-action-queue.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js ***!
  \**************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// This module can be shared between both pages router and app router\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"onRecoverableError\", ({\n    enumerable: true,\n    get: function() {\n        return onRecoverableError;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _bailouttocsr = __webpack_require__(/*! ../../shared/lib/lazy-dynamic/bailout-to-csr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js\");\nconst _reportglobalerror = __webpack_require__(/*! ./report-global-error */ \"(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/report-global-error.js\");\nconst _stitchederror = __webpack_require__(/*! ../components/errors/stitched-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/stitched-error.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../lib/is-error */ \"(app-pages-browser)/./node_modules/next/dist/lib/is-error.js\"));\nconst onRecoverableError = (error, errorInfo)=>{\n    // x-ref: https://github.com/facebook/react/pull/28736\n    const cause = (0, _iserror.default)(error) && 'cause' in error ? error.cause : error;\n    const stitchedError = (0, _stitchederror.getReactStitchedError)(cause);\n    // In development mode, pass along the component stack to the error\n    if ( true && errorInfo.componentStack) {\n        ;\n        stitchedError._componentStack = errorInfo.componentStack;\n    }\n    // Skip certain custom errors which are not expected to be reported on client\n    if ((0, _bailouttocsr.isBailoutToCSRError)(cause)) return;\n    (0, _reportglobalerror.reportGlobalError)(stitchedError);\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=on-recoverable-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js ***!
  \**********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    PathParamsContext: function() {\n        return PathParamsContext;\n    },\n    PathnameContext: function() {\n        return PathnameContext;\n    },\n    SearchParamsContext: function() {\n        return SearchParamsContext;\n    }\n});\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst SearchParamsContext = (0, _react.createContext)(null);\nconst PathnameContext = (0, _react.createContext)(null);\nconst PathParamsContext = (0, _react.createContext)(null);\nif (true) {\n    SearchParamsContext.displayName = 'SearchParamsContext';\n    PathnameContext.displayName = 'PathnameContext';\n    PathParamsContext.displayName = 'PathParamsContext';\n} //# sourceMappingURL=hooks-client-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9ob29rcy1jbGllbnQtY29udGV4dC5zaGFyZWQtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7SUFPYUEsaUJBQWlCO2VBQWpCQTs7SUFEQUMsZUFBZTtlQUFmQTs7SUFEQUMsbUJBQW1CO2VBQW5CQTs7O21DQUhpQjtBQUd2QixNQUFNQSxzQkFBc0JDLENBQUFBLEdBQUFBLE9BQUFBLGFBQUFBLEVBQXNDO0FBQ2xFLE1BQU1GLGtCQUFrQkUsQ0FBQUEsR0FBQUEsT0FBQUEsYUFBQUEsRUFBNkI7QUFDckQsTUFBTUgsb0JBQW9CRyxDQUFBQSxHQUFBQSxPQUFBQSxhQUFBQSxFQUE2QjtBQUU5RCxJQUFJQyxJQUFvQixFQUFtQjtJQUN6Q0Ysb0JBQW9CSyxXQUFXLEdBQUc7SUFDbENOLGdCQUFnQk0sV0FBVyxHQUFHO0lBQzlCUCxrQkFBa0JPLFdBQVcsR0FBRztBQUNsQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBU1VTXFxzcmNcXHNoYXJlZFxcbGliXFxob29rcy1jbGllbnQtY29udGV4dC5zaGFyZWQtcnVudGltZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgY3JlYXRlQ29udGV4dCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHR5cGUgeyBQYXJhbXMgfSBmcm9tICcuLi8uLi9zZXJ2ZXIvcmVxdWVzdC9wYXJhbXMnXG5cbmV4cG9ydCBjb25zdCBTZWFyY2hQYXJhbXNDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxVUkxTZWFyY2hQYXJhbXMgfCBudWxsPihudWxsKVxuZXhwb3J0IGNvbnN0IFBhdGhuYW1lQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8c3RyaW5nIHwgbnVsbD4obnVsbClcbmV4cG9ydCBjb25zdCBQYXRoUGFyYW1zQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8UGFyYW1zIHwgbnVsbD4obnVsbClcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgU2VhcmNoUGFyYW1zQ29udGV4dC5kaXNwbGF5TmFtZSA9ICdTZWFyY2hQYXJhbXNDb250ZXh0J1xuICBQYXRobmFtZUNvbnRleHQuZGlzcGxheU5hbWUgPSAnUGF0aG5hbWVDb250ZXh0J1xuICBQYXRoUGFyYW1zQ29udGV4dC5kaXNwbGF5TmFtZSA9ICdQYXRoUGFyYW1zQ29udGV4dCdcbn1cbiJdLCJuYW1lcyI6WyJQYXRoUGFyYW1zQ29udGV4dCIsIlBhdGhuYW1lQ29udGV4dCIsIlNlYXJjaFBhcmFtc0NvbnRleHQiLCJjcmVhdGVDb250ZXh0IiwicHJvY2VzcyIsImVudiIsIk5PREVfRU5WIiwiZGlzcGxheU5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\n"));

/***/ })

});