export const API_ENDPOINTS = {
    LOGIN: '/api/Account/login',
    LOGOUT: '/api/Account/logout',
    REGISTER: '/api/Account/register',
    REFRESH_TOKEN: '/api/Account/refresh',
    USER_INFO: '/api/User/me',
    PROFILE_INFO: '/api/User/getprofileinfo',
    UPDATE_PROFILE: '/api/User/update-profile',
    PROFILE_PICTURE: '/api/User/profile-picture',
    DELETE_PROFILE_PICTURE: '/api/User/delete-profile-picture',
    USER_ADDRESSES: '/api/User/addresses',
    DELETE_ADDRESS: '/api/User/delete-address',
    SET_DEFAULT_ADDRESS: '/api/User/set-default-address',
    CREATE_ADDRESS: '/api/User/add-address',
    ADD_REFERENCE: '/api/User/Account/add-reference',
    MY_REF_CODE: '/api/User/my-ref-code',
    MAKE_ADMIN: '/api/User/make-admin',
    TEST_AUTH: '/api/Account/test',
    DEBUG_CLAIMS: '/api/Account/debug-claims',

    // Product API endpoints
    GET_BRANDS: '/api/Products/brands',
    GET_SUBCATEGORIES: '/api/Products',
    CREATE_FULL_PRODUCT: '/api/Products/create-full-product',
    CREATE_DEALERSHIP_PRODUCT: '/api/Products/create-dealership-product',
    ADD_PRODUCT_IMAGE: '/api/Products/addimage',
    DELETE_PRODUCT_IMAGE: '/api/Products/image',
    REPLACE_PRODUCT_IMAGE: '/api/Products/image/replace',
    GET_ADMIN_PRODUCTS: '/api/Products/products-admin',
    DELETE_PRODUCT: '/api/Products/deleteproduct',
    GET_PRODUCTS: '/api/Products/getproducts',
    GET_PRODUCT_DETAIL: '/api/Products/productdetail',
    GET_PRODUCT_VARIANTS: '/api/Products/{productId}/variants',
    GET_ADMIN_PRODUCT_STATISTICS: '/api/Products/admin/product-statistics',
    GET_CATEGORIES_BY_BRAND: '/api/Products/categories-by-brand',
    GET_FEATURE_VALUES: '/api/Products/feature-values',
    GET_PRODUCT_FEATURES: '/api/Products/product-features',

    // Category Management API endpoints
    CREATE_BRAND: '/api/Products/createbrand',
    CREATE_CATEGORY: '/api/Products/createcategory',
    CREATE_SUBCATEGORY: '/api/Products/createsubcategory',
    CREATE_FEATURE_DEFINITION: '/api/Products/createdefinition',
    CREATE_FEATURE_VALUE: '/api/Products/createvalue',
    CREATE_SUBCATEGORY_FEATURE: '/api/Products/createsubfeature',
    CREATE_BRAND_CATEGORY: '/api/Products/brand-category',
    GET_CATEGORIES: '/api/Products/categories',
    GET_SUBCATEGORY_FEATURES: '/api/Products/subcategoryfeatures',
    GET_SUBCATEGORY_FEATURES_BY_ID: '/api/Products/subcategoryfeatures/{subCategoryId}',
    GET_FEATURE_VALUES_BY_DEFINITION_ID: '/api/Products/feature-values/{definitionId}',
    GET_PRODUCT_FEATURES_BY_PRODUCT_ID: '/api/Products/product-features/{productId}',
    GET_CATEGORIES_BY_BRAND_ID: '/api/Products/categories-by-brand/{brandId}',
    GET_SUBCATEGORIES_BY_CATEGORY: '/api/Products/{categoryId}/subcategories',
    GET_ALL_FEATURE_DEFINITIONS: '/api/Products/features',
    UPDATE_FULL_PRODUCT: '/api/Products/update-full-product',
    UPDATE_PRODUCT_STATUS: '/api/Products/updateproductstatus',
    GET_PRODUCT_MESSAGE: '/api/Products/productmessage',

    // User Management API endpoints
    GET_USERS: '/api/User/getusers',
    GET_USER_ROLE_COUNTS: '/api/User/user-role-counts',
    GET_DISCOUNT_RATE: '/api/User/discount-rate',
    UPDATE_CART_TYPE: '/api/User/update-cart-type',

    // My Products API endpoints
    GET_MY_PRODUCTS: '/api/Products/my-products',
    GET_MY_PRODUCT_STATISTICS: '/api/Products/myproductstats',
    UPDATE_SIMPLE_PRODUCT: '/api/Products/update-simple',
    GET_DEALERSHIP_PRODUCT_DETAIL: '/api/Products/dealership-product-detail',

    // Public Product Catalog API endpoints
    FILTER_PRODUCTS: '/api/catalog/products/filter',
    GET_REFERENCE_DATA: '/api/catalog/reference-data',
    GET_CATALOG_PRODUCT_DETAIL: '/api/catalog/product-detail',

    // Cart API endpoints
    ADD_TO_CART: '/api/User/cart/add',
    GET_CART_ITEMS: '/api/User/cart/items',
    GET_CART_COUNT: '/api/User/cart/count',
    REMOVE_FROM_CART: '/api/User/cart/remove',
    UPDATE_CART_QUANTITY: '/api/User/cart/update-quantity',
};