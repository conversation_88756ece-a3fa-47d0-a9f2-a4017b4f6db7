import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';
import { AuthUser, LoginCredentials, RegisterData, MembershipLevelIds } from '@/types';
import { authService, ProfileInfoResponse } from '@/services/authService';
import { useAuthStore } from '@/stores/authStore';
import { addressKeys } from './useAddresses';
import { discountRateKeys } from './useDiscountRate';
import { useRouter } from 'next/navigation';
import { useCustomerPriceStore } from '@/stores/customerPriceStore';

// 🏭 Query Key Factory
export const authKeys = {
    all: ['auth'] as const,
    user: () => [...authKeys.all, 'user'] as const,
    profile: () => [...authKeys.all, 'profile'] as const,
    profileInfo: () => [...authKeys.all, 'profileInfo'] as const,
    refCode: () => [...authKeys.all, 'refCode'] as const,
} as const;

// 🔍 User Info Query - Optimized caching
export const useUserInfo = () => {
    const router = useRouter();

    const query = useQuery<AuthUser | null, Error>({
        queryKey: authKeys.user(),
        queryFn: async (): Promise<AuthUser | null> => {
            try {
                const userData = await authService.getUserInfo();

                if (!userData) {
                    return null;
                }

                // Map backend data to frontend format
                let user = userData.user || userData;

                // Handle missing user data - Backend artık userId kullanıyor
                if (!user.userId && !user.id && !user.email) {
                    user = {
                        userId: 1,
                        email: '<EMAIL>',
                        firstName: 'User',
                        lastName: 'User',
                        phoneNumber: '',
                        isActive: true,
                        registeredAt: new Date().toISOString(),
                        membershipLevelId: 1,
                        careerRankId: 1,
                        referenceId: 0,
                        roles: ['Customer']
                    };
                }

                // Map to AuthUser type properly - Backend artık userId kullanıyor
                const mappedUser: AuthUser = {
                    id: user.userId !== undefined ? user.userId : user.id !== undefined ? user.id : 1,
                    firstName: user.firstName || 'User',
                    lastName: user.lastName || 'User',
                    email: user.email || '<EMAIL>',
                    phoneNumber: user.phoneNumber || '',
                    isActive: user.isActive !== undefined ? user.isActive : true,
                    registeredAt: user.registeredAt || new Date().toISOString(),
                    membershipLevelId: user.membershipLevelId !== undefined ? user.membershipLevelId : 1,
                    careerRankId: user.careerRankId !== undefined ? user.careerRankId : 1,
                    referenceId: user.referenceId !== undefined ? user.referenceId : user.referanceId !== undefined ? user.referanceId : 0,
                    roles: user.roles || (user.role ? [user.role] : ['Customer']),
                    role: user.role
                        ? user.role.toLowerCase()
                        : user.roles && user.roles.includes('Admin') ? 'admin'
                            : user.roles && user.roles.includes('Dealership') ? 'dealership'
                                : 'customer',
                    membershipLevel: (user.membershipLevelId !== undefined ? user.membershipLevelId : 0) as MembershipLevelIds,
                    joinDate: user.registeredAt ? new Date(user.registeredAt).toISOString().split('T')[0] : '',
                    isDealershipApproved: user.roles && user.roles.includes('Dealership')
                };

                return mappedUser;
            } catch (error) {
                throw error;
            }
        },

        // Query her zaman aktif - JWT sistem artık doğru çalışıyor
        enabled: true,

        // 📝 Cache Strategy - VERY IMPORTANT for performance
        staleTime: 15 * 60 * 1000, // 15 minutes - user data rarely changes
        gcTime: 30 * 60 * 1000, // 30 minutes - keep in memory

        // 🔄 Refetch Strategy - Solve focus problems
        refetchOnWindowFocus: false, // ✅ Bu focus sorununu çözer!
        refetchOnMount: 'always', // Only refetch if stale
        refetchOnReconnect: true, // Refetch when internet reconnects

        // ⚡ Background Updates
        refetchInterval: false, // No automatic polling
        refetchIntervalInBackground: false,

        // 🛡️ Error Handling
        retry: (failureCount, error: any) => {
            // Don't retry on auth errors (401, 403)
            if (error?.response?.status === 401 || error?.response?.status === 403) {
                return false;
            }
            return failureCount < 2;
        },
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),

        throwOnError: (error: any) => {
            // Sadece 401 (Unauthorized) hatası DIŞINDAKİ hataları fırlat.
            // 401 hatası bizim için "kullanıcı giriş yapmamış" demek, bu bir çökme hatası değil.
            // Böylece Next.js'in geliştirme overlay'i gereksiz yere tetiklenmez.
            return error.response?.status !== 401;
        },
    });

    // 📊 Handle query state changes with useEffect (TanStack Query v5 best practice)
    useEffect(() => {
        if (query.isSuccess && query.data) {
            useAuthStore.setState({
                user: query.data,
                isAuthenticated: true,
                error: null
            });
        }
    }, [query.isSuccess, query.data]);

    useEffect(() => {
        if (query.isError && query.error) {

            // Handle auth errors
            if ((query.error as any)?.response?.status === 401) {
                useAuthStore.setState({
                    user: null,
                    isAuthenticated: false
                });
            }
        }
    }, [query.isError, query.error]);

    return query;
};

// 🔐 Login Mutation - Optimized
export const useLoginMutation = () => {
    const queryClient = useQueryClient();
    const router = useRouter();
    const { user, setUser, clearAuth, setLoading } = useAuthStore();

    // Login mutation'ı artık bir AuthUser değil, işlemin başarısını (boolean) döndürür.
    return useMutation<boolean, Error, LoginCredentials>({
        mutationFn: async (credentials: LoginCredentials) => {
            const success = await authService.login(credentials.email, credentials.password);

            if (!success) {
                throw new Error('Login failed: Invalid credentials from service');
            }

            return success; // Sadece true döner
        },

        // Login başarılı olunca (true dönünce) bu blok çalışır.
        onSuccess: async () => {

            try {
                // 1. User query'sini geçersiz kıl (stale olarak işaretle).
                await queryClient.invalidateQueries({ queryKey: authKeys.user() });
                // 2. Geçersiz kılınan query'yi hemen fetch et ve gerçek kullanıcı verisini al.
                const userFromApi = await queryClient.fetchQuery<AuthUser>({ queryKey: authKeys.user() });

                if (!userFromApi || !userFromApi.id) {
                    throw new Error('Fetched user data is invalid or missing ID.');
                }

                // 3. Alınan gerçek veriyle AuthStore'u güncelle.
                useAuthStore.setState({
                    isAuthenticated: true,
                    error: null,
                    isLoading: false,
                    user: userFromApi,
                });

                // 4. Yeni kullanıcı için adres cache'ini temizle.
                queryClient.invalidateQueries({ queryKey: addressKeys.all });
                if (userFromApi.id) {
                    queryClient.invalidateQueries({ queryKey: addressKeys.list(userFromApi.id) });
                    queryClient.removeQueries({ queryKey: addressKeys.list(userFromApi.id) });
                }

                // 5. Yeni kullanıcı için discount rate cache'ini temizle ve yeniden çek.
                queryClient.invalidateQueries({ queryKey: discountRateKeys.all });
                if (userFromApi.id) {
                    queryClient.invalidateQueries({ queryKey: discountRateKeys.user(userFromApi.id) });
                    queryClient.removeQueries({ queryKey: discountRateKeys.user(userFromApi.id) });
                }

                // 6. Customer price state'ini resetle
                useCustomerPriceStore.getState().resetCustomerPrice();

                // 7. Sepet cache'ini temizle ve yeniden çek (giriş sonrası sepet sayısı güncellensin)
                queryClient.invalidateQueries({ queryKey: ['cartCount'] });
                queryClient.invalidateQueries({ queryKey: ['cartItems'] });

                try {
                    // await get().checkAuth(); // 🗑️ Silindi: Artık store'da checkAuth yok. Invalidate yeterli.
                    console.log('✅ Background checkAuth başarılı - user bilgisi güncellendi');
                } catch (checkAuthError: any) {
                    console.log('⚠️ Background checkAuth başarısız - mevcut user bilgisi korunuyor:', checkAuthError.message);
                }

            } catch (error) {
                useAuthStore.setState({
                    error: 'Giriş başarılı fakat kullanıcı verileri alınamadı.',
                    isAuthenticated: false,
                    user: null,
                });
            }
        },

        onError: (error: any) => {
            useAuthStore.setState({
                error: error.response?.data?.message || 'Giriş başarısız',
                isAuthenticated: false,
                user: null,
            });
        },
    });
};

// 🚪 Logout Mutation - Optimized  
export const useLogoutMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async () => {
            await authService.logout();
        },

        onSuccess: () => {
            // Tüm cache'i temizle (discount rate dahil)
            queryClient.clear();
            useAuthStore.setState({
                user: null,
                isAuthenticated: false,
                error: null
            });
            // Customer price state'ini resetle
            useCustomerPriceStore.getState().resetCustomerPrice();
        },

        onError: (error) => {
            // Hata durumunda da tüm cache'i temizle
            queryClient.clear();
            useAuthStore.setState({
                user: null,
                isAuthenticated: false
            });
            // Customer price state'ini resetle
            useCustomerPriceStore.getState().resetCustomerPrice();
        }
    });
};

// 📝 Register Mutation
export const useRegisterMutation = () => {
    return useMutation<boolean, Error, RegisterData>({
        mutationFn: async (data: RegisterData) => {
            if (data.password !== data.confirmPassword) {
                throw new Error('Şifreler eşleşmiyor');
            }

            const response = await authService.register({
                firstName: data.firstName,
                lastName: data.lastName,
                email: data.email,
                password: data.password,
                phoneNumber: data.phoneNumber || '',
                referansCode: data.referansCode,
            });

            // Başarı durumunu backend'den gelen response'a göre belirle
            // Örnek: return response.success;
            return response.success;
        },
        onSuccess: () => {
            // Kayıt başarılı olunca ne yapılacağına burada karar verilir.
            // Şimdilik sadece başarılı kabul ediyoruz, otomatik login yapmıyoruz.
            // İstenirse burada login mutation'ı tetiklenebilir.
            useAuthStore.setState({ error: null });
        },
        onError: (error: any) => {
            useAuthStore.setState({
                error: error.message || 'Kayıt başarısız'
            });
        }
    });
};

// 🔄 Manual Cache Utils
export const useAuthCacheUtils = () => {
    const queryClient = useQueryClient();

    return {
        // Force refresh user data
        refreshUser: () => {
            return queryClient.invalidateQueries({ queryKey: authKeys.user() });
        },

        // Get cached user data
        getCachedUser: (): AuthUser | null => {
            return queryClient.getQueryData<AuthUser | null>(authKeys.user()) || null;
        },

        // Update cached user data
        updateCachedUser: (userData: AuthUser) => {
            queryClient.setQueryData(authKeys.user(), userData);
        },

        // Clear all auth cache
        clearAuthCache: () => {
            queryClient.removeQueries({ queryKey: authKeys.all });
        }
    };
};

// 🎯 Profile Info Query - Detaylı profil bilgileri (fotoğraf URL'si dahil)
export const useProfileInfo = () => {

    const query = useQuery<ProfileInfoResponse | null, Error>({
        queryKey: authKeys.profileInfo(),
        queryFn: async (): Promise<ProfileInfoResponse | null> => {
            try {
                const profileData = await authService.getProfileInfo();
                console.log('📋 Profile Info Data:', profileData);
                return profileData;
            } catch (error) {
                console.error('❌ Profile Info Error:', error);
                throw error;
            }
        },

        // Query her zaman aktif - JWT sistem artık doğru çalışıyor
        enabled: true,

        // Cache Strategy
        staleTime: 5 * 60 * 1000, // 5 dakika - profil bilgileri daha sık güncellenebilir
        gcTime: 15 * 60 * 1000, // 15 dakika

        // Refetch Strategy
        refetchOnWindowFocus: false,
        refetchOnMount: 'always',
        refetchOnReconnect: true,

        // Background Updates
        refetchInterval: false,
        refetchIntervalInBackground: false,

        // Error Handling
        retry: (failureCount, error: any) => {
            if (error?.response?.status === 401 || error?.response?.status === 403) {
                return false;
            }
            return failureCount < 2;
        },
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),

        throwOnError: (error: any) => {
            return error.response?.status !== 401;
        },
    });

    return query;
};

// 🔗 My Reference Code Query - Kullanıcının referans kodunu getir
export const useMyRefCode = () => {
    const query = useQuery<{ code: string } | null, Error>({
        queryKey: authKeys.refCode(),
        queryFn: async (): Promise<{ code: string } | null> => {
            try {
                const refCodeData = await authService.getMyRefCode();
                console.log('🔗 Referans kodu alındı:', refCodeData);
                return refCodeData;
            } catch (error) {
                console.error('❌ Referans kodu alınırken hata:', error);
                throw error;
            }
        },

        // Query her zaman aktif - JWT sistem artık doğru çalışıyor
        enabled: true,

        // Cache Strategy
        staleTime: 30 * 60 * 1000, // 30 dakika - referans kodu nadiren değişir
        gcTime: 60 * 60 * 1000, // 1 saat

        // Refetch Strategy
        refetchOnWindowFocus: false,
        refetchOnMount: 'always',
        refetchOnReconnect: true,

        // Background Updates
        refetchInterval: false,
        refetchIntervalInBackground: false,

        // Error Handling
        retry: (failureCount, error: any) => {
            if (error?.response?.status === 401 || error?.response?.status === 403) {
                return false;
            }
            return failureCount < 2;
        },
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),

        throwOnError: (error: any) => {
            return error.response?.status !== 401;
        },
    });

    return query;
};