"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useBaseQuery: () => (/* binding */ useBaseQuery)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/query-core */ \"(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/query-core */ \"(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QueryClientProvider.js */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _QueryErrorResetBoundary_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QueryErrorResetBoundary.js */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js\");\n/* harmony import */ var _errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./errorBoundaryUtils.js */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js\");\n/* harmony import */ var _IsRestoringProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./IsRestoringProvider.js */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js\");\n/* harmony import */ var _suspense_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./suspense.js */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/suspense.js\");\n/* __next_internal_client_entry_do_not_use__ useBaseQuery auto */ var _s = $RefreshSig$();\n// src/useBaseQuery.ts\n\n\n\n\n\n\n\nfunction useBaseQuery(options, Observer, queryClient) {\n    var _client_getDefaultOptions_queries__experimental_beforeQuery, _client_getDefaultOptions_queries, _client_getDefaultOptions_queries__experimental_afterQuery, _client_getDefaultOptions_queries1;\n    _s();\n    if (true) {\n        if (typeof options !== \"object\" || Array.isArray(options)) {\n            throw new Error('Bad argument type. Starting with v5, only the \"Object\" form is allowed when calling query related functions. Please use the error stack to find the culprit call. More info here: https://tanstack.com/query/latest/docs/react/guides/migrating-to-v5#supports-a-single-signature-one-object');\n        }\n    }\n    const isRestoring = (0,_IsRestoringProvider_js__WEBPACK_IMPORTED_MODULE_1__.useIsRestoring)();\n    const errorResetBoundary = (0,_QueryErrorResetBoundary_js__WEBPACK_IMPORTED_MODULE_2__.useQueryErrorResetBoundary)();\n    const client = (0,_QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)(queryClient);\n    const defaultedOptions = client.defaultQueryOptions(options);\n    (_client_getDefaultOptions_queries = client.getDefaultOptions().queries) === null || _client_getDefaultOptions_queries === void 0 ? void 0 : (_client_getDefaultOptions_queries__experimental_beforeQuery = _client_getDefaultOptions_queries._experimental_beforeQuery) === null || _client_getDefaultOptions_queries__experimental_beforeQuery === void 0 ? void 0 : _client_getDefaultOptions_queries__experimental_beforeQuery.call(_client_getDefaultOptions_queries, defaultedOptions);\n    if (true) {\n        if (!defaultedOptions.queryFn) {\n            console.error(\"[\".concat(defaultedOptions.queryHash, \"]: No queryFn was passed as an option, and no default queryFn was found. The queryFn parameter is only optional when using a default queryFn. More info here: https://tanstack.com/query/latest/docs/framework/react/guides/default-query-function\"));\n        }\n    }\n    defaultedOptions._optimisticResults = isRestoring ? \"isRestoring\" : \"optimistic\";\n    (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.ensureSuspenseTimers)(defaultedOptions);\n    (0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.ensurePreventErrorBoundaryRetry)(defaultedOptions, errorResetBoundary);\n    (0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.useClearResetErrorBoundary)(errorResetBoundary);\n    const isNewCacheEntry = !client.getQueryCache().get(defaultedOptions.queryHash);\n    const [observer] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        \"useBaseQuery.useState\": ()=>new Observer(client, defaultedOptions)\n    }[\"useBaseQuery.useState\"]);\n    const result = observer.getOptimisticResult(defaultedOptions);\n    const shouldSubscribe = !isRestoring && options.subscribed !== false;\n    react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"useBaseQuery.useSyncExternalStore.useCallback\": (onStoreChange)=>{\n            const unsubscribe = shouldSubscribe ? observer.subscribe(_tanstack_query_core__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batchCalls(onStoreChange)) : _tanstack_query_core__WEBPACK_IMPORTED_MODULE_7__.noop;\n            observer.updateResult();\n            return unsubscribe;\n        }\n    }[\"useBaseQuery.useSyncExternalStore.useCallback\"], [\n        observer,\n        shouldSubscribe\n    ]), {\n        \"useBaseQuery.useSyncExternalStore\": ()=>observer.getCurrentResult()\n    }[\"useBaseQuery.useSyncExternalStore\"], {\n        \"useBaseQuery.useSyncExternalStore\": ()=>observer.getCurrentResult()\n    }[\"useBaseQuery.useSyncExternalStore\"]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useBaseQuery.useEffect\": ()=>{\n            observer.setOptions(defaultedOptions);\n        }\n    }[\"useBaseQuery.useEffect\"], [\n        defaultedOptions,\n        observer\n    ]);\n    if ((0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.shouldSuspend)(defaultedOptions, result)) {\n        throw (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.fetchOptimistic)(defaultedOptions, observer, errorResetBoundary);\n    }\n    if ((0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.getHasError)({\n        result,\n        errorResetBoundary,\n        throwOnError: defaultedOptions.throwOnError,\n        query: client.getQueryCache().get(defaultedOptions.queryHash),\n        suspense: defaultedOptions.suspense\n    })) {\n        throw result.error;\n    }\n    ;\n    (_client_getDefaultOptions_queries1 = client.getDefaultOptions().queries) === null || _client_getDefaultOptions_queries1 === void 0 ? void 0 : (_client_getDefaultOptions_queries__experimental_afterQuery = _client_getDefaultOptions_queries1._experimental_afterQuery) === null || _client_getDefaultOptions_queries__experimental_afterQuery === void 0 ? void 0 : _client_getDefaultOptions_queries__experimental_afterQuery.call(_client_getDefaultOptions_queries1, defaultedOptions, result);\n    if (defaultedOptions.experimental_prefetchInRender && !_tanstack_query_core__WEBPACK_IMPORTED_MODULE_7__.isServer && (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.willFetch)(result, isRestoring)) {\n        var // subscribe to the \"cache promise\" so that we can finalize the currentThenable once data comes in\n        _client_getQueryCache_get;\n        const promise = isNewCacheEntry ? // Fetch immediately on render in order to ensure `.promise` is resolved even if the component is unmounted\n        (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.fetchOptimistic)(defaultedOptions, observer, errorResetBoundary) : (_client_getQueryCache_get = client.getQueryCache().get(defaultedOptions.queryHash)) === null || _client_getQueryCache_get === void 0 ? void 0 : _client_getQueryCache_get.promise;\n        promise === null || promise === void 0 ? void 0 : promise.catch(_tanstack_query_core__WEBPACK_IMPORTED_MODULE_7__.noop).finally(()=>{\n            observer.updateResult();\n        });\n    }\n    return !defaultedOptions.notifyOnChangeProps ? observer.trackResult(result) : result;\n}\n_s(useBaseQuery, \"cxBOUWOIDVWlStMma/Gipkq6xxM=\", false, function() {\n    return [\n        _IsRestoringProvider_js__WEBPACK_IMPORTED_MODULE_1__.useIsRestoring,\n        _QueryErrorResetBoundary_js__WEBPACK_IMPORTED_MODULE_2__.useQueryErrorResetBoundary,\n        _QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_3__.useQueryClient,\n        _errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.useClearResetErrorBoundary\n    ];\n});\n //# sourceMappingURL=useBaseQuery.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/motion/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createRendererMotionComponent: () => (/* binding */ createRendererMotionComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/errors.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../context/LayoutGroupContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/LayoutGroupContext.mjs\");\n/* harmony import */ var _context_LazyContext_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../context/LazyContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/LazyContext.mjs\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/MotionConfigContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* harmony import */ var _context_MotionContext_index_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../context/MotionContext/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionContext/index.mjs\");\n/* harmony import */ var _context_MotionContext_create_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../context/MotionContext/create.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionContext/create.mjs\");\n/* harmony import */ var _utils_is_browser_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/is-browser.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/is-browser.mjs\");\n/* harmony import */ var _features_definitions_mjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./features/definitions.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/definitions.mjs\");\n/* harmony import */ var _features_load_features_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./features/load-features.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/load-features.mjs\");\n/* harmony import */ var _utils_symbol_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./utils/symbol.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/symbol.mjs\");\n/* harmony import */ var _utils_use_motion_ref_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./utils/use-motion-ref.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/use-motion-ref.mjs\");\n/* harmony import */ var _utils_use_visual_element_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/use-visual-element.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/use-visual-element.mjs\");\n/* __next_internal_client_entry_do_not_use__ createRendererMotionComponent auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Create a `motion` component.\n *\n * This function accepts a Component argument, which can be either a string (ie \"div\"\n * for `motion.div`), or an actual React component.\n *\n * Alongside this is a config option which provides a way of rendering the provided\n * component \"offline\", or outside the React render cycle.\n */ function createRendererMotionComponent(param) {\n    let { preloadedFeatures, createVisualElement, useRender, useVisualState, Component } = param;\n    var _s = $RefreshSig$();\n    preloadedFeatures && (0,_features_load_features_mjs__WEBPACK_IMPORTED_MODULE_2__.loadFeatures)(preloadedFeatures);\n    function MotionComponent(props, externalRef) {\n        _s();\n        /**\n         * If we need to measure the element we load this functionality in a\n         * separate class component in order to gain access to getSnapshotBeforeUpdate.\n         */ let MeasureLayout;\n        const configAndProps = {\n            ...(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__.MotionConfigContext),\n            ...props,\n            layoutId: useLayoutId(props)\n        };\n        const { isStatic } = configAndProps;\n        const context = (0,_context_MotionContext_create_mjs__WEBPACK_IMPORTED_MODULE_4__.useCreateMotionContext)(props);\n        const visualState = useVisualState(props, isStatic);\n        if (!isStatic && _utils_is_browser_mjs__WEBPACK_IMPORTED_MODULE_5__.isBrowser) {\n            useStrictMode(configAndProps, preloadedFeatures);\n            const layoutProjection = getProjectionFunctionality(configAndProps);\n            MeasureLayout = layoutProjection.MeasureLayout;\n            /**\n             * Create a VisualElement for this component. A VisualElement provides a common\n             * interface to renderer-specific APIs (ie DOM/Three.js etc) as well as\n             * providing a way of rendering to these APIs outside of the React render loop\n             * for more performant animations and interactions\n             */ context.visualElement = (0,_utils_use_visual_element_mjs__WEBPACK_IMPORTED_MODULE_6__.useVisualElement)(Component, visualState, configAndProps, createVisualElement, layoutProjection.ProjectionNode);\n        }\n        /**\n         * The mount order and hierarchy is specific to ensure our element ref\n         * is hydrated by the time features fire their effects.\n         */ return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_context_MotionContext_index_mjs__WEBPACK_IMPORTED_MODULE_7__.MotionContext.Provider, {\n            value: context,\n            children: [\n                MeasureLayout && context.visualElement ? (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(MeasureLayout, {\n                    visualElement: context.visualElement,\n                    ...configAndProps\n                }) : null,\n                useRender(Component, props, (0,_utils_use_motion_ref_mjs__WEBPACK_IMPORTED_MODULE_8__.useMotionRef)(visualState, context.visualElement, externalRef), visualState, isStatic, context.visualElement)\n            ]\n        });\n    }\n    _s(MotionComponent, \"OzmmWP8E2WLE0LhHHUY21ioDbYk=\", false, function() {\n        return [\n            useLayoutId,\n            _context_MotionContext_create_mjs__WEBPACK_IMPORTED_MODULE_4__.useCreateMotionContext,\n            useVisualState,\n            _utils_use_motion_ref_mjs__WEBPACK_IMPORTED_MODULE_8__.useMotionRef,\n            useRender\n        ];\n    });\n    var _Component_displayName, _ref;\n    MotionComponent.displayName = \"motion.\".concat(typeof Component === \"string\" ? Component : \"create(\".concat((_ref = (_Component_displayName = Component.displayName) !== null && _Component_displayName !== void 0 ? _Component_displayName : Component.name) !== null && _ref !== void 0 ? _ref : \"\", \")\"));\n    const ForwardRefMotionComponent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(MotionComponent);\n    ForwardRefMotionComponent[_utils_symbol_mjs__WEBPACK_IMPORTED_MODULE_9__.motionComponentSymbol] = Component;\n    return ForwardRefMotionComponent;\n}\nfunction useLayoutId(param) {\n    let { layoutId } = param;\n    _s();\n    const layoutGroupId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_10__.LayoutGroupContext).id;\n    return layoutGroupId && layoutId !== undefined ? layoutGroupId + \"-\" + layoutId : layoutId;\n}\n_s(useLayoutId, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nfunction useStrictMode(configAndProps, preloadedFeatures) {\n    _s1();\n    const isStrict = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_LazyContext_mjs__WEBPACK_IMPORTED_MODULE_11__.LazyContext).strict;\n    /**\n     * If we're in development mode, check to make sure we're not rendering a motion component\n     * as a child of LazyMotion, as this will break the file-size benefits of using it.\n     */ if ( true && preloadedFeatures && isStrict) {\n        const strictMessage = \"You have rendered a `motion` component within a `LazyMotion` component. This will break tree shaking. Import and render a `m` component instead.\";\n        configAndProps.ignoreStrict ? (0,motion_utils__WEBPACK_IMPORTED_MODULE_12__.warning)(false, strictMessage) : (0,motion_utils__WEBPACK_IMPORTED_MODULE_12__.invariant)(false, strictMessage);\n    }\n}\n_s1(useStrictMode, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nfunction getProjectionFunctionality(props) {\n    const { drag, layout } = _features_definitions_mjs__WEBPACK_IMPORTED_MODULE_13__.featureDefinitions;\n    if (!drag && !layout) return {};\n    const combined = {\n        ...drag,\n        ...layout\n    };\n    return {\n        MeasureLayout: (drag === null || drag === void 0 ? void 0 : drag.isEnabled(props)) || (layout === null || layout === void 0 ? void 0 : layout.isEnabled(props)) ? combined.MeasureLayout : undefined,\n        ProjectionNode: combined.ProjectionNode\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/client/app-dir/link.js ***!
  \*******************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    /**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */ default: function() {\n        return LinkComponent;\n    },\n    useLinkStatus: function() {\n        return useLinkStatus;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _formaturl = __webpack_require__(/*! ../../shared/lib/router/utils/format-url */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _routerreducertypes = __webpack_require__(/*! ../components/router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _usemergedref = __webpack_require__(/*! ../use-merged-ref */ \"(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js\");\nconst _utils = __webpack_require__(/*! ../../shared/lib/utils */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _warnonce = __webpack_require__(/*! ../../shared/lib/utils/warn-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nconst _links = __webpack_require__(/*! ../components/links */ \"(app-pages-browser)/./node_modules/next/dist/client/components/links.js\");\nconst _islocalurl = __webpack_require__(/*! ../../shared/lib/router/utils/is-local-url */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _approuterinstance = __webpack_require__(/*! ../components/app-router-instance */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\");\nconst _erroronce = __webpack_require__(/*! ../../shared/lib/utils/error-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js\");\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute('target');\n    return target && target !== '_self' || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === 'A';\n    if (isAnchorNodeName && isModifiedEvent(e) || e.currentTarget.hasAttribute('download')) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    if (!(0, _islocalurl.isLocalURL)(href)) {\n        if (replace) {\n            // browser default behavior does not replace the history state\n            // so we need to do it manually\n            e.preventDefault();\n            location.replace(href);\n        }\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    const navigate = ()=>{\n        if (onNavigate) {\n            let isDefaultPrevented = false;\n            onNavigate({\n                preventDefault: ()=>{\n                    isDefaultPrevented = true;\n                }\n            });\n            if (isDefaultPrevented) {\n                return;\n            }\n        }\n        (0, _approuterinstance.dispatchNavigateAction)(as || href, replace ? 'replace' : 'push', scroll != null ? scroll : true, linkInstanceRef.current);\n    };\n    _react.default.startTransition(navigate);\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === 'string') {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\nfunction LinkComponent(props) {\n    _s();\n    const [linkStatus, setOptimisticLinkStatus] = (0, _react.useOptimistic)(_links.IDLE_LINK_STATUS);\n    let children;\n    const linkInstanceRef = (0, _react.useRef)(null);\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, onClick, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, onNavigate, ref: forwardedRef, unstable_dynamicOnHover, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === 'string' || typeof children === 'number')) {\n        children = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            children: children\n        });\n    }\n    const router = _react.default.useContext(_approutercontextsharedruntime.AppRouterContext);\n    const prefetchEnabled = prefetchProp !== false;\n    /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */ const appPrefetchKind = prefetchProp === null ? _routerreducertypes.PrefetchKind.AUTO : _routerreducertypes.PrefetchKind.FULL;\n    if (true) {\n        function createPropError(args) {\n            return Object.defineProperty(new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + ( true ? \"\\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                value: \"E319\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === 'href') {\n                if (props[key] == null || typeof props[key] !== 'string' && typeof props[key] !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: props[key] === null ? 'null' : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            unstable_dynamicOnHover: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true,\n            onNavigate: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === 'as') {\n                if (props[key] && valType !== 'string' && valType !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'onClick' || key === 'onMouseEnter' || key === 'onTouchStart' || key === 'onNavigate') {\n                if (props[key] && valType !== 'function') {\n                    throw createPropError({\n                        key,\n                        expected: '`function`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'replace' || key === 'scroll' || key === 'shallow' || key === 'passHref' || key === 'prefetch' || key === 'legacyBehavior' || key === 'unstable_dynamicOnHover') {\n                if (props[key] != null && valType !== 'boolean') {\n                    throw createPropError({\n                        key,\n                        expected: '`boolean`',\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n    }\n    if (true) {\n        if (props.locale) {\n            (0, _warnonce.warnOnce)('The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization');\n        }\n        if (!asProp) {\n            let href;\n            if (typeof hrefProp === 'string') {\n                href = hrefProp;\n            } else if (typeof hrefProp === 'object' && typeof hrefProp.pathname === 'string') {\n                href = hrefProp.pathname;\n            }\n            if (href) {\n                const hasDynamicSegment = href.split('/').some((segment)=>segment.startsWith('[') && segment.endsWith(']'));\n                if (hasDynamicSegment) {\n                    throw Object.defineProperty(new Error(\"Dynamic href `\" + href + \"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E267\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n        }\n    }\n    const { href, as } = _react.default.useMemo({\n        \"LinkComponent.useMemo\": ()=>{\n            const resolvedHref = formatStringOrUrl(hrefProp);\n            return {\n                href: resolvedHref,\n                as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n            };\n        }\n    }[\"LinkComponent.useMemo\"], [\n        hrefProp,\n        asProp\n    ]);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw Object.defineProperty(new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E320\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                throw Object.defineProperty(new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + ( true ? \" \\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                    value: \"E266\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === 'a') {\n                throw Object.defineProperty(new Error('Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E209\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === 'object' && child.ref : forwardedRef;\n    // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n    // mount. In the future we will also use this to keep track of all the\n    // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n    // a revalidation or refresh.\n    const observeLinkVisibilityOnMount = _react.default.useCallback({\n        \"LinkComponent.useCallback[observeLinkVisibilityOnMount]\": (element)=>{\n            if (router !== null) {\n                linkInstanceRef.current = (0, _links.mountLinkInstance)(element, href, router, appPrefetchKind, prefetchEnabled, setOptimisticLinkStatus);\n            }\n            return ({\n                \"LinkComponent.useCallback[observeLinkVisibilityOnMount]\": ()=>{\n                    if (linkInstanceRef.current) {\n                        (0, _links.unmountLinkForCurrentNavigation)(linkInstanceRef.current);\n                        linkInstanceRef.current = null;\n                    }\n                    (0, _links.unmountPrefetchableInstance)(element);\n                }\n            })[\"LinkComponent.useCallback[observeLinkVisibilityOnMount]\"];\n        }\n    }[\"LinkComponent.useCallback[observeLinkVisibilityOnMount]\"], [\n        prefetchEnabled,\n        href,\n        router,\n        appPrefetchKind,\n        setOptimisticLinkStatus\n    ]);\n    const mergedRef = (0, _usemergedref.useMergedRef)(observeLinkVisibilityOnMount, childRef);\n    const childProps = {\n        ref: mergedRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw Object.defineProperty(new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E312\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n            if (!legacyBehavior && typeof onClick === 'function') {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === 'function') {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === 'function') {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled || \"development\" === 'development') {\n                return;\n            }\n            const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;\n            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);\n        },\n        onTouchStart:  false ? 0 : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === 'function') {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled) {\n                return;\n            }\n            const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;\n            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the basePath.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === 'a' && !('href' in child.props)) {\n        childProps.href = (0, _addbasepath.addBasePath)(as);\n    }\n    let link;\n    if (legacyBehavior) {\n        if (true) {\n            (0, _erroronce.errorOnce)('`legacyBehavior` is deprecated and will be removed in a future ' + 'release. A codemod is available to upgrade your components:\\n\\n' + 'npx @next/codemod@latest new-link .\\n\\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components');\n        }\n        link = /*#__PURE__*/ _react.default.cloneElement(child, childProps);\n    } else {\n        link = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            ...restProps,\n            ...childProps,\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(LinkStatusContext.Provider, {\n        value: linkStatus,\n        children: link\n    });\n}\n_s(LinkComponent, \"MNV6IdWv8Lu3MKc7Fm4v59uGRY0=\");\n_c = LinkComponent;\nconst LinkStatusContext = /*#__PURE__*/ (0, _react.createContext)(_links.IDLE_LINK_STATUS);\nconst useLinkStatus = ()=>{\n    return (0, _react.useContext)(LinkStatusContext);\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c;\n$RefreshReg$(_c, \"LinkComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/format-url.js ***!
  \**********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    formatUrl: function() {\n        return formatUrl;\n    },\n    formatWithValidation: function() {\n        return formatWithValidation;\n    },\n    urlObjectKeys: function() {\n        return urlObjectKeys;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _querystring = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./querystring */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js\"));\nconst slashedProtocols = /https?|ftp|gopher|file/;\nfunction formatUrl(urlObj) {\n    let { auth, hostname } = urlObj;\n    let protocol = urlObj.protocol || '';\n    let pathname = urlObj.pathname || '';\n    let hash = urlObj.hash || '';\n    let query = urlObj.query || '';\n    let host = false;\n    auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : '';\n    if (urlObj.host) {\n        host = auth + urlObj.host;\n    } else if (hostname) {\n        host = auth + (~hostname.indexOf(':') ? \"[\" + hostname + \"]\" : hostname);\n        if (urlObj.port) {\n            host += ':' + urlObj.port;\n        }\n    }\n    if (query && typeof query === 'object') {\n        query = String(_querystring.urlQueryToSearchParams(query));\n    }\n    let search = urlObj.search || query && \"?\" + query || '';\n    if (protocol && !protocol.endsWith(':')) protocol += ':';\n    if (urlObj.slashes || (!protocol || slashedProtocols.test(protocol)) && host !== false) {\n        host = '//' + (host || '');\n        if (pathname && pathname[0] !== '/') pathname = '/' + pathname;\n    } else if (!host) {\n        host = '';\n    }\n    if (hash && hash[0] !== '#') hash = '#' + hash;\n    if (search && search[0] !== '?') search = '?' + search;\n    pathname = pathname.replace(/[?#]/g, encodeURIComponent);\n    search = search.replace('#', '%23');\n    return \"\" + protocol + host + pathname + search + hash;\n}\nconst urlObjectKeys = [\n    'auth',\n    'hash',\n    'host',\n    'hostname',\n    'href',\n    'path',\n    'pathname',\n    'port',\n    'protocol',\n    'query',\n    'search',\n    'slashes'\n];\nfunction formatWithValidation(url) {\n    if (true) {\n        if (url !== null && typeof url === 'object') {\n            Object.keys(url).forEach((key)=>{\n                if (!urlObjectKeys.includes(key)) {\n                    console.warn(\"Unknown key passed via urlObject into url.format: \" + key);\n                }\n            });\n        }\n    }\n    return formatUrl(url);\n} //# sourceMappingURL=format-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils.js ***!
  \****************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DecodeError: function() {\n        return DecodeError;\n    },\n    MiddlewareNotFoundError: function() {\n        return MiddlewareNotFoundError;\n    },\n    MissingStaticPage: function() {\n        return MissingStaticPage;\n    },\n    NormalizeError: function() {\n        return NormalizeError;\n    },\n    PageNotFoundError: function() {\n        return PageNotFoundError;\n    },\n    SP: function() {\n        return SP;\n    },\n    ST: function() {\n        return ST;\n    },\n    WEB_VITALS: function() {\n        return WEB_VITALS;\n    },\n    execOnce: function() {\n        return execOnce;\n    },\n    getDisplayName: function() {\n        return getDisplayName;\n    },\n    getLocationOrigin: function() {\n        return getLocationOrigin;\n    },\n    getURL: function() {\n        return getURL;\n    },\n    isAbsoluteUrl: function() {\n        return isAbsoluteUrl;\n    },\n    isResSent: function() {\n        return isResSent;\n    },\n    loadGetInitialProps: function() {\n        return loadGetInitialProps;\n    },\n    normalizeRepeatedSlashes: function() {\n        return normalizeRepeatedSlashes;\n    },\n    stringifyError: function() {\n        return stringifyError;\n    }\n});\nconst WEB_VITALS = [\n    'CLS',\n    'FCP',\n    'FID',\n    'INP',\n    'LCP',\n    'TTFB'\n];\nfunction execOnce(fn) {\n    let used = false;\n    let result;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        if (!used) {\n            used = true;\n            result = fn(...args);\n        }\n        return result;\n    };\n}\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nconst isAbsoluteUrl = (url)=>ABSOLUTE_URL_REGEX.test(url);\nfunction getLocationOrigin() {\n    const { protocol, hostname, port } = window.location;\n    return protocol + \"//\" + hostname + (port ? ':' + port : '');\n}\nfunction getURL() {\n    const { href } = window.location;\n    const origin = getLocationOrigin();\n    return href.substring(origin.length);\n}\nfunction getDisplayName(Component) {\n    return typeof Component === 'string' ? Component : Component.displayName || Component.name || 'Unknown';\n}\nfunction isResSent(res) {\n    return res.finished || res.headersSent;\n}\nfunction normalizeRepeatedSlashes(url) {\n    const urlParts = url.split('?');\n    const urlNoQuery = urlParts[0];\n    return urlNoQuery // first we replace any non-encoded backslashes with forward\n    // then normalize repeated forward slashes\n    .replace(/\\\\/g, '/').replace(/\\/\\/+/g, '/') + (urlParts[1] ? \"?\" + urlParts.slice(1).join('?') : '');\n}\nasync function loadGetInitialProps(App, ctx) {\n    if (true) {\n        var _App_prototype;\n        if ((_App_prototype = App.prototype) == null ? void 0 : _App_prototype.getInitialProps) {\n            const message = '\"' + getDisplayName(App) + '.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';\n            throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n                value: \"E394\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n    }\n    // when called from _app `ctx` is nested in `ctx`\n    const res = ctx.res || ctx.ctx && ctx.ctx.res;\n    if (!App.getInitialProps) {\n        if (ctx.ctx && ctx.Component) {\n            // @ts-ignore pageProps default\n            return {\n                pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx)\n            };\n        }\n        return {};\n    }\n    const props = await App.getInitialProps(ctx);\n    if (res && isResSent(res)) {\n        return props;\n    }\n    if (!props) {\n        const message = '\"' + getDisplayName(App) + '.getInitialProps()\" should resolve to an object. But found \"' + props + '\" instead.';\n        throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    if (true) {\n        if (Object.keys(props).length === 0 && !ctx.ctx) {\n            console.warn(\"\" + getDisplayName(App) + \" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\");\n        }\n    }\n    return props;\n}\nconst SP = typeof performance !== 'undefined';\nconst ST = SP && [\n    'mark',\n    'measure',\n    'getEntriesByName'\n].every((method)=>typeof performance[method] === 'function');\nclass DecodeError extends Error {\n}\nclass NormalizeError extends Error {\n}\nclass PageNotFoundError extends Error {\n    constructor(page){\n        super();\n        this.code = 'ENOENT';\n        this.name = 'PageNotFoundError';\n        this.message = \"Cannot find module for page: \" + page;\n    }\n}\nclass MissingStaticPage extends Error {\n    constructor(page, message){\n        super();\n        this.message = \"Failed to load static file for page: \" + page + \" \" + message;\n    }\n}\nclass MiddlewareNotFoundError extends Error {\n    constructor(){\n        super();\n        this.code = 'ENOENT';\n        this.message = \"Cannot find the middleware module\";\n    }\n}\nfunction stringifyError(error) {\n    return JSON.stringify({\n        message: error.message,\n        stack: error.stack\n    });\n} //# sourceMappingURL=utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi91dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7SUFvYWFBLFdBQVc7ZUFBWEE7O0lBb0JBQyx1QkFBdUI7ZUFBdkJBOztJQVBBQyxpQkFBaUI7ZUFBakJBOztJQVpBQyxjQUFjO2VBQWRBOztJQUNBQyxpQkFBaUI7ZUFBakJBOztJQVRBQyxFQUFFO2VBQUZBOztJQUNBQyxFQUFFO2VBQUZBOztJQWxYQUMsVUFBVTtlQUFWQTs7SUFzUUdDLFFBQVE7ZUFBUkE7O0lBK0JBQyxjQUFjO2VBQWRBOztJQVhBQyxpQkFBaUI7ZUFBakJBOztJQUtBQyxNQUFNO2VBQU5BOztJQVBIQyxhQUFhO2VBQWJBOztJQW1CR0MsU0FBUztlQUFUQTs7SUFrQk1DLG1CQUFtQjtlQUFuQkE7O0lBZE5DLHdCQUF3QjtlQUF4QkE7O0lBK0dBQyxjQUFjO2VBQWRBOzs7QUE5WlQsTUFBTVQsYUFBYTtJQUFDO0lBQU87SUFBTztJQUFPO0lBQU87SUFBTztDQUFPO0FBc1E5RCxTQUFTQyxTQUNkUyxFQUFLO0lBRUwsSUFBSUMsT0FBTztJQUNYLElBQUlDO0lBRUosT0FBUTt5Q0FBSUMsT0FBQUEsSUFBQUEsTUFBQUEsT0FBQUEsT0FBQUEsR0FBQUEsT0FBQUEsTUFBQUEsT0FBQUE7WUFBQUEsSUFBQUEsQ0FBQUEsS0FBQUEsR0FBQUEsU0FBQUEsQ0FBQUEsS0FBQUE7O1FBQ1YsSUFBSSxDQUFDRixNQUFNO1lBQ1RBLE9BQU87WUFDUEMsU0FBU0YsTUFBTUc7UUFDakI7UUFDQSxPQUFPRDtJQUNUO0FBQ0Y7QUFFQSwwREFBMEQ7QUFDMUQsZ0VBQWdFO0FBQ2hFLE1BQU1FLHFCQUFxQjtBQUNwQixNQUFNVCxnQkFBZ0IsQ0FBQ1UsTUFBZ0JELG1CQUFtQkUsSUFBSSxDQUFDRDtBQUUvRCxTQUFTWjtJQUNkLE1BQU0sRUFBRWMsUUFBUSxFQUFFQyxRQUFRLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPQyxRQUFRO0lBQ3BELE9BQVVKLFdBQVMsT0FBSUMsV0FBV0MsUUFBTyxNQUFNQSxPQUFPLEdBQUM7QUFDekQ7QUFFTyxTQUFTZjtJQUNkLE1BQU0sRUFBRWtCLElBQUksRUFBRSxHQUFHRixPQUFPQyxRQUFRO0lBQ2hDLE1BQU1FLFNBQVNwQjtJQUNmLE9BQU9tQixLQUFLRSxTQUFTLENBQUNELE9BQU9FLE1BQU07QUFDckM7QUFFTyxTQUFTdkIsZUFBa0J3QixTQUEyQjtJQUMzRCxPQUFPLE9BQU9BLGNBQWMsV0FDeEJBLFlBQ0FBLFVBQVVDLFdBQVcsSUFBSUQsVUFBVUUsSUFBSSxJQUFJO0FBQ2pEO0FBRU8sU0FBU3RCLFVBQVV1QixHQUFtQjtJQUMzQyxPQUFPQSxJQUFJQyxRQUFRLElBQUlELElBQUlFLFdBQVc7QUFDeEM7QUFFTyxTQUFTdkIseUJBQXlCTyxHQUFXO0lBQ2xELE1BQU1pQixXQUFXakIsSUFBSWtCLEtBQUssQ0FBQztJQUMzQixNQUFNQyxhQUFhRixRQUFRLENBQUMsRUFBRTtJQUU5QixPQUNFRSxXQUNFLDREQUE0RDtJQUM1RCwwQ0FBMEM7S0FDekNDLE9BQU8sQ0FBQyxPQUFPLEtBQ2ZBLE9BQU8sQ0FBQyxVQUFVLE9BQ3BCSCxDQUFBQSxRQUFRLENBQUMsRUFBRSxHQUFJLE1BQUdBLFNBQVNJLEtBQUssQ0FBQyxHQUFHQyxJQUFJLENBQUMsT0FBUyxHQUFDO0FBRXhEO0FBRU8sZUFBZTlCLG9CQUlwQitCLEdBQWdDLEVBQUVDLEdBQU07SUFDeEMsSUFBSUMsSUFBb0IsRUFBbUI7WUFDckNGO1FBQUosS0FBSUEsaUJBQUFBLElBQUlLLFNBQUFBLEtBQVMsZ0JBQWJMLGVBQWVNLGVBQWUsRUFBRTtZQUNsQyxNQUFNQyxVQUFXLE1BQUczQyxlQUNsQm9DLE9BQ0E7WUFDRixNQUFNLHFCQUFrQixDQUFsQixJQUFJUSxNQUFNRCxVQUFWO3VCQUFBOzRCQUFBOzhCQUFBO1lBQWlCO1FBQ3pCO0lBQ0Y7SUFDQSxpREFBaUQ7SUFDakQsTUFBTWhCLE1BQU1VLElBQUlWLEdBQUcsSUFBS1UsSUFBSUEsR0FBRyxJQUFJQSxJQUFJQSxHQUFHLENBQUNWLEdBQUc7SUFFOUMsSUFBSSxDQUFDUyxJQUFJTSxlQUFlLEVBQUU7UUFDeEIsSUFBSUwsSUFBSUEsR0FBRyxJQUFJQSxJQUFJYixTQUFTLEVBQUU7WUFDNUIsK0JBQStCO1lBQy9CLE9BQU87Z0JBQ0xxQixXQUFXLE1BQU14QyxvQkFBb0JnQyxJQUFJYixTQUFTLEVBQUVhLElBQUlBLEdBQUc7WUFDN0Q7UUFDRjtRQUNBLE9BQU8sQ0FBQztJQUNWO0lBRUEsTUFBTVMsUUFBUSxNQUFNVixJQUFJTSxlQUFlLENBQUNMO0lBRXhDLElBQUlWLE9BQU92QixVQUFVdUIsTUFBTTtRQUN6QixPQUFPbUI7SUFDVDtJQUVBLElBQUksQ0FBQ0EsT0FBTztRQUNWLE1BQU1ILFVBQVcsTUFBRzNDLGVBQ2xCb0MsT0FDQSxpRUFBOERVLFFBQU07UUFDdEUsTUFBTSxxQkFBa0IsQ0FBbEIsSUFBSUYsTUFBTUQsVUFBVjttQkFBQTt3QkFBQTswQkFBQTtRQUFpQjtJQUN6QjtJQUVBLElBQUlMLElBQW9CLEVBQW1CO1FBQ3pDLElBQUlTLE9BQU9DLElBQUksQ0FBQ0YsT0FBT3ZCLE1BQU0sS0FBSyxLQUFLLENBQUNjLElBQUlBLEdBQUcsRUFBRTtZQUMvQ1ksUUFBUUMsSUFBSSxDQUNULEtBQUVsRCxlQUNEb0MsT0FDQTtRQUVOO0lBQ0Y7SUFFQSxPQUFPVTtBQUNUO0FBRU8sTUFBTWxELEtBQUssT0FBT3VELGdCQUFnQjtBQUNsQyxNQUFNdEQsS0FDWEQsTUFDQztJQUFDO0lBQVE7SUFBVztDQUFtQixDQUFXd0QsS0FBSyxDQUN0RCxDQUFDQyxTQUFXLE9BQU9GLFdBQVcsQ0FBQ0UsT0FBTyxLQUFLO0FBR3hDLE1BQU05RCxvQkFBb0JxRDtBQUFPO0FBQ2pDLE1BQU1sRCx1QkFBdUJrRDtBQUFPO0FBQ3BDLE1BQU1qRCwwQkFBMEJpRDtJQUdyQ1UsWUFBWUMsSUFBWSxDQUFFO1FBQ3hCLEtBQUs7UUFDTCxJQUFJLENBQUNDLElBQUksR0FBRztRQUNaLElBQUksQ0FBQzlCLElBQUksR0FBRztRQUNaLElBQUksQ0FBQ2lCLE9BQU8sR0FBSSxrQ0FBK0JZO0lBQ2pEO0FBQ0Y7QUFFTyxNQUFNOUQsMEJBQTBCbUQ7SUFDckNVLFlBQVlDLElBQVksRUFBRVosT0FBZSxDQUFFO1FBQ3pDLEtBQUs7UUFDTCxJQUFJLENBQUNBLE9BQU8sR0FBSSwwQ0FBdUNZLE9BQUssTUFBR1o7SUFDakU7QUFDRjtBQUVPLE1BQU1uRCxnQ0FBZ0NvRDtJQUUzQ1UsYUFBYztRQUNaLEtBQUs7UUFDTCxJQUFJLENBQUNFLElBQUksR0FBRztRQUNaLElBQUksQ0FBQ2IsT0FBTyxHQUFJO0lBQ2xCO0FBQ0Y7QUFXTyxTQUFTcEMsZUFBZWtELEtBQVk7SUFDekMsT0FBT0MsS0FBS0MsU0FBUyxDQUFDO1FBQUVoQixTQUFTYyxNQUFNZCxPQUFPO1FBQUVpQixPQUFPSCxNQUFNRyxLQUFLO0lBQUM7QUFDckUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQVNVU1xcc3JjXFxzaGFyZWRcXGxpYlxcdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBIdG1sUHJvcHMgfSBmcm9tICcuL2h0bWwtY29udGV4dC5zaGFyZWQtcnVudGltZSdcbmltcG9ydCB0eXBlIHsgQ29tcG9uZW50VHlwZSwgSlNYIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgdHlwZSB7IERvbWFpbkxvY2FsZSB9IGZyb20gJy4uLy4uL3NlcnZlci9jb25maWcnXG5pbXBvcnQgdHlwZSB7IEVudiB9IGZyb20gJ0BuZXh0L2VudidcbmltcG9ydCB0eXBlIHsgSW5jb21pbmdNZXNzYWdlLCBTZXJ2ZXJSZXNwb25zZSB9IGZyb20gJ2h0dHAnXG5pbXBvcnQgdHlwZSB7IE5leHRSb3V0ZXIgfSBmcm9tICcuL3JvdXRlci9yb3V0ZXInXG5pbXBvcnQgdHlwZSB7IFBhcnNlZFVybFF1ZXJ5IH0gZnJvbSAncXVlcnlzdHJpbmcnXG5pbXBvcnQgdHlwZSB7IFByZXZpZXdEYXRhIH0gZnJvbSAnLi4vLi4vdHlwZXMnXG5pbXBvcnQgdHlwZSB7IENPTVBJTEVSX05BTUVTIH0gZnJvbSAnLi9jb25zdGFudHMnXG5pbXBvcnQgdHlwZSBmcyBmcm9tICdmcydcblxuZXhwb3J0IHR5cGUgTmV4dENvbXBvbmVudFR5cGU8XG4gIENvbnRleHQgZXh0ZW5kcyBCYXNlQ29udGV4dCA9IE5leHRQYWdlQ29udGV4dCxcbiAgSW5pdGlhbFByb3BzID0ge30sXG4gIFByb3BzID0ge30sXG4+ID0gQ29tcG9uZW50VHlwZTxQcm9wcz4gJiB7XG4gIC8qKlxuICAgKiBVc2VkIGZvciBpbml0aWFsIHBhZ2UgbG9hZCBkYXRhIHBvcHVsYXRpb24uIERhdGEgcmV0dXJuZWQgZnJvbSBgZ2V0SW5pdGlhbFByb3BzYCBpcyBzZXJpYWxpemVkIHdoZW4gc2VydmVyIHJlbmRlcmVkLlxuICAgKiBNYWtlIHN1cmUgdG8gcmV0dXJuIHBsYWluIGBPYmplY3RgIHdpdGhvdXQgdXNpbmcgYERhdGVgLCBgTWFwYCwgYFNldGAuXG4gICAqIEBwYXJhbSBjb250ZXh0IENvbnRleHQgb2YgYHBhZ2VgXG4gICAqL1xuICBnZXRJbml0aWFsUHJvcHM/KGNvbnRleHQ6IENvbnRleHQpOiBJbml0aWFsUHJvcHMgfCBQcm9taXNlPEluaXRpYWxQcm9wcz5cbn1cblxuZXhwb3J0IHR5cGUgRG9jdW1lbnRUeXBlID0gTmV4dENvbXBvbmVudFR5cGU8XG4gIERvY3VtZW50Q29udGV4dCxcbiAgRG9jdW1lbnRJbml0aWFsUHJvcHMsXG4gIERvY3VtZW50UHJvcHNcbj5cblxuZXhwb3J0IHR5cGUgQXBwVHlwZTxQID0ge30+ID0gTmV4dENvbXBvbmVudFR5cGU8XG4gIEFwcENvbnRleHRUeXBlLFxuICBQLFxuICBBcHBQcm9wc1R5cGU8YW55LCBQPlxuPlxuXG5leHBvcnQgdHlwZSBBcHBUcmVlVHlwZSA9IENvbXBvbmVudFR5cGU8XG4gIEFwcEluaXRpYWxQcm9wcyAmIHsgW25hbWU6IHN0cmluZ106IGFueSB9XG4+XG5cbi8qKlxuICogV2ViIHZpdGFscyBwcm92aWRlZCB0byBfYXBwLnJlcG9ydFdlYlZpdGFscyBieSBDb3JlIFdlYiBWaXRhbHMgcGx1Z2luIGRldmVsb3BlZCBieSBHb29nbGUgQ2hyb21lIHRlYW0uXG4gKiBodHRwczovL25leHRqcy5vcmcvYmxvZy9uZXh0LTktNCNpbnRlZ3JhdGVkLXdlYi12aXRhbHMtcmVwb3J0aW5nXG4gKi9cbmV4cG9ydCBjb25zdCBXRUJfVklUQUxTID0gWydDTFMnLCAnRkNQJywgJ0ZJRCcsICdJTlAnLCAnTENQJywgJ1RURkInXSBhcyBjb25zdFxuZXhwb3J0IHR5cGUgTmV4dFdlYlZpdGFsc01ldHJpYyA9IHtcbiAgaWQ6IHN0cmluZ1xuICBzdGFydFRpbWU6IG51bWJlclxuICB2YWx1ZTogbnVtYmVyXG4gIGF0dHJpYnV0aW9uPzogeyBba2V5OiBzdHJpbmddOiB1bmtub3duIH1cbn0gJiAoXG4gIHwge1xuICAgICAgbGFiZWw6ICd3ZWItdml0YWwnXG4gICAgICBuYW1lOiAodHlwZW9mIFdFQl9WSVRBTFMpW251bWJlcl1cbiAgICB9XG4gIHwge1xuICAgICAgbGFiZWw6ICdjdXN0b20nXG4gICAgICBuYW1lOlxuICAgICAgICB8ICdOZXh0LmpzLWh5ZHJhdGlvbidcbiAgICAgICAgfCAnTmV4dC5qcy1yb3V0ZS1jaGFuZ2UtdG8tcmVuZGVyJ1xuICAgICAgICB8ICdOZXh0LmpzLXJlbmRlcidcbiAgICB9XG4pXG5cbmV4cG9ydCB0eXBlIEVuaGFuY2VyPEM+ID0gKENvbXBvbmVudDogQykgPT4gQ1xuXG5leHBvcnQgdHlwZSBDb21wb25lbnRzRW5oYW5jZXIgPVxuICB8IHtcbiAgICAgIGVuaGFuY2VBcHA/OiBFbmhhbmNlcjxBcHBUeXBlPlxuICAgICAgZW5oYW5jZUNvbXBvbmVudD86IEVuaGFuY2VyPE5leHRDb21wb25lbnRUeXBlPlxuICAgIH1cbiAgfCBFbmhhbmNlcjxOZXh0Q29tcG9uZW50VHlwZT5cblxuZXhwb3J0IHR5cGUgUmVuZGVyUGFnZVJlc3VsdCA9IHtcbiAgaHRtbDogc3RyaW5nXG4gIGhlYWQ/OiBBcnJheTxKU1guRWxlbWVudCB8IG51bGw+XG59XG5cbmV4cG9ydCB0eXBlIFJlbmRlclBhZ2UgPSAoXG4gIG9wdGlvbnM/OiBDb21wb25lbnRzRW5oYW5jZXJcbikgPT4gRG9jdW1lbnRJbml0aWFsUHJvcHMgfCBQcm9taXNlPERvY3VtZW50SW5pdGlhbFByb3BzPlxuXG5leHBvcnQgdHlwZSBCYXNlQ29udGV4dCA9IHtcbiAgcmVzPzogU2VydmVyUmVzcG9uc2VcbiAgW2s6IHN0cmluZ106IGFueVxufVxuXG5leHBvcnQgdHlwZSBORVhUX0RBVEEgPSB7XG4gIHByb3BzOiBSZWNvcmQ8c3RyaW5nLCBhbnk+XG4gIHBhZ2U6IHN0cmluZ1xuICBxdWVyeTogUGFyc2VkVXJsUXVlcnlcbiAgYnVpbGRJZDogc3RyaW5nXG4gIGFzc2V0UHJlZml4Pzogc3RyaW5nXG4gIHJ1bnRpbWVDb25maWc/OiB7IFtrZXk6IHN0cmluZ106IGFueSB9XG4gIG5leHRFeHBvcnQ/OiBib29sZWFuXG4gIGF1dG9FeHBvcnQ/OiBib29sZWFuXG4gIGlzRmFsbGJhY2s/OiBib29sZWFuXG4gIGlzRXhwZXJpbWVudGFsQ29tcGlsZT86IGJvb2xlYW5cbiAgZHluYW1pY0lkcz86IChzdHJpbmcgfCBudW1iZXIpW11cbiAgZXJyPzogRXJyb3IgJiB7XG4gICAgc3RhdHVzQ29kZT86IG51bWJlclxuICAgIHNvdXJjZT86IHR5cGVvZiBDT01QSUxFUl9OQU1FUy5zZXJ2ZXIgfCB0eXBlb2YgQ09NUElMRVJfTkFNRVMuZWRnZVNlcnZlclxuICB9XG4gIGdzcD86IGJvb2xlYW5cbiAgZ3NzcD86IGJvb2xlYW5cbiAgY3VzdG9tU2VydmVyPzogYm9vbGVhblxuICBnaXA/OiBib29sZWFuXG4gIGFwcEdpcD86IGJvb2xlYW5cbiAgbG9jYWxlPzogc3RyaW5nXG4gIGxvY2FsZXM/OiByZWFkb25seSBzdHJpbmdbXVxuICBkZWZhdWx0TG9jYWxlPzogc3RyaW5nXG4gIGRvbWFpbkxvY2FsZXM/OiByZWFkb25seSBEb21haW5Mb2NhbGVbXVxuICBzY3JpcHRMb2FkZXI/OiBhbnlbXVxuICBpc1ByZXZpZXc/OiBib29sZWFuXG4gIG5vdEZvdW5kU3JjUGFnZT86IHN0cmluZ1xufVxuXG4vKipcbiAqIGBOZXh0YCBjb250ZXh0XG4gKi9cbmV4cG9ydCBpbnRlcmZhY2UgTmV4dFBhZ2VDb250ZXh0IHtcbiAgLyoqXG4gICAqIEVycm9yIG9iamVjdCBpZiBlbmNvdW50ZXJlZCBkdXJpbmcgcmVuZGVyaW5nXG4gICAqL1xuICBlcnI/OiAoRXJyb3IgJiB7IHN0YXR1c0NvZGU/OiBudW1iZXIgfSkgfCBudWxsXG4gIC8qKlxuICAgKiBgSFRUUGAgcmVxdWVzdCBvYmplY3QuXG4gICAqL1xuICByZXE/OiBJbmNvbWluZ01lc3NhZ2VcbiAgLyoqXG4gICAqIGBIVFRQYCByZXNwb25zZSBvYmplY3QuXG4gICAqL1xuICByZXM/OiBTZXJ2ZXJSZXNwb25zZVxuICAvKipcbiAgICogUGF0aCBzZWN0aW9uIG9mIGBVUkxgLlxuICAgKi9cbiAgcGF0aG5hbWU6IHN0cmluZ1xuICAvKipcbiAgICogUXVlcnkgc3RyaW5nIHNlY3Rpb24gb2YgYFVSTGAgcGFyc2VkIGFzIGFuIG9iamVjdC5cbiAgICovXG4gIHF1ZXJ5OiBQYXJzZWRVcmxRdWVyeVxuICAvKipcbiAgICogYFN0cmluZ2Agb2YgdGhlIGFjdHVhbCBwYXRoIGluY2x1ZGluZyBxdWVyeS5cbiAgICovXG4gIGFzUGF0aD86IHN0cmluZ1xuICAvKipcbiAgICogVGhlIGN1cnJlbnRseSBhY3RpdmUgbG9jYWxlXG4gICAqL1xuICBsb2NhbGU/OiBzdHJpbmdcbiAgLyoqXG4gICAqIEFsbCBjb25maWd1cmVkIGxvY2FsZXNcbiAgICovXG4gIGxvY2FsZXM/OiByZWFkb25seSBzdHJpbmdbXVxuICAvKipcbiAgICogVGhlIGNvbmZpZ3VyZWQgZGVmYXVsdCBsb2NhbGVcbiAgICovXG4gIGRlZmF1bHRMb2NhbGU/OiBzdHJpbmdcbiAgLyoqXG4gICAqIGBDb21wb25lbnRgIHRoZSB0cmVlIG9mIHRoZSBBcHAgdG8gdXNlIGlmIG5lZWRpbmcgdG8gcmVuZGVyIHNlcGFyYXRlbHlcbiAgICovXG4gIEFwcFRyZWU6IEFwcFRyZWVUeXBlXG59XG5cbmV4cG9ydCB0eXBlIEFwcENvbnRleHRUeXBlPFJvdXRlciBleHRlbmRzIE5leHRSb3V0ZXIgPSBOZXh0Um91dGVyPiA9IHtcbiAgQ29tcG9uZW50OiBOZXh0Q29tcG9uZW50VHlwZTxOZXh0UGFnZUNvbnRleHQ+XG4gIEFwcFRyZWU6IEFwcFRyZWVUeXBlXG4gIGN0eDogTmV4dFBhZ2VDb250ZXh0XG4gIHJvdXRlcjogUm91dGVyXG59XG5cbmV4cG9ydCB0eXBlIEFwcEluaXRpYWxQcm9wczxQYWdlUHJvcHMgPSBhbnk+ID0ge1xuICBwYWdlUHJvcHM6IFBhZ2VQcm9wc1xufVxuXG5leHBvcnQgdHlwZSBBcHBQcm9wc1R5cGU8XG4gIFJvdXRlciBleHRlbmRzIE5leHRSb3V0ZXIgPSBOZXh0Um91dGVyLFxuICBQYWdlUHJvcHMgPSB7fSxcbj4gPSBBcHBJbml0aWFsUHJvcHM8UGFnZVByb3BzPiAmIHtcbiAgQ29tcG9uZW50OiBOZXh0Q29tcG9uZW50VHlwZTxOZXh0UGFnZUNvbnRleHQsIGFueSwgYW55PlxuICByb3V0ZXI6IFJvdXRlclxuICBfX05fU1NHPzogYm9vbGVhblxuICBfX05fU1NQPzogYm9vbGVhblxufVxuXG5leHBvcnQgdHlwZSBEb2N1bWVudENvbnRleHQgPSBOZXh0UGFnZUNvbnRleHQgJiB7XG4gIHJlbmRlclBhZ2U6IFJlbmRlclBhZ2VcbiAgZGVmYXVsdEdldEluaXRpYWxQcm9wcyhcbiAgICBjdHg6IERvY3VtZW50Q29udGV4dCxcbiAgICBvcHRpb25zPzogeyBub25jZT86IHN0cmluZyB9XG4gICk6IFByb21pc2U8RG9jdW1lbnRJbml0aWFsUHJvcHM+XG59XG5cbmV4cG9ydCB0eXBlIERvY3VtZW50SW5pdGlhbFByb3BzID0gUmVuZGVyUGFnZVJlc3VsdCAmIHtcbiAgc3R5bGVzPzogUmVhY3QuUmVhY3RFbGVtZW50W10gfCBJdGVyYWJsZTxSZWFjdC5SZWFjdE5vZGU+IHwgSlNYLkVsZW1lbnRcbn1cblxuZXhwb3J0IHR5cGUgRG9jdW1lbnRQcm9wcyA9IERvY3VtZW50SW5pdGlhbFByb3BzICYgSHRtbFByb3BzXG5cbi8qKlxuICogTmV4dCBgQVBJYCByb3V0ZSByZXF1ZXN0XG4gKi9cbmV4cG9ydCBpbnRlcmZhY2UgTmV4dEFwaVJlcXVlc3QgZXh0ZW5kcyBJbmNvbWluZ01lc3NhZ2Uge1xuICAvKipcbiAgICogT2JqZWN0IG9mIGBxdWVyeWAgdmFsdWVzIGZyb20gdXJsXG4gICAqL1xuICBxdWVyeTogUGFydGlhbDx7XG4gICAgW2tleTogc3RyaW5nXTogc3RyaW5nIHwgc3RyaW5nW11cbiAgfT5cbiAgLyoqXG4gICAqIE9iamVjdCBvZiBgY29va2llc2AgZnJvbSBoZWFkZXJcbiAgICovXG4gIGNvb2tpZXM6IFBhcnRpYWw8e1xuICAgIFtrZXk6IHN0cmluZ106IHN0cmluZ1xuICB9PlxuXG4gIGJvZHk6IGFueVxuXG4gIGVudjogRW52XG5cbiAgZHJhZnRNb2RlPzogYm9vbGVhblxuXG4gIHByZXZpZXc/OiBib29sZWFuXG4gIC8qKlxuICAgKiBQcmV2aWV3IGRhdGEgc2V0IG9uIHRoZSByZXF1ZXN0LCBpZiBhbnlcbiAgICogKi9cbiAgcHJldmlld0RhdGE/OiBQcmV2aWV3RGF0YVxufVxuXG4vKipcbiAqIFNlbmQgYm9keSBvZiByZXNwb25zZVxuICovXG50eXBlIFNlbmQ8VD4gPSAoYm9keTogVCkgPT4gdm9pZFxuXG4vKipcbiAqIE5leHQgYEFQSWAgcm91dGUgcmVzcG9uc2VcbiAqL1xuZXhwb3J0IHR5cGUgTmV4dEFwaVJlc3BvbnNlPERhdGEgPSBhbnk+ID0gU2VydmVyUmVzcG9uc2UgJiB7XG4gIC8qKlxuICAgKiBTZW5kIGRhdGEgYGFueWAgZGF0YSBpbiByZXNwb25zZVxuICAgKi9cbiAgc2VuZDogU2VuZDxEYXRhPlxuICAvKipcbiAgICogU2VuZCBkYXRhIGBqc29uYCBkYXRhIGluIHJlc3BvbnNlXG4gICAqL1xuICBqc29uOiBTZW5kPERhdGE+XG4gIHN0YXR1czogKHN0YXR1c0NvZGU6IG51bWJlcikgPT4gTmV4dEFwaVJlc3BvbnNlPERhdGE+XG4gIHJlZGlyZWN0KHVybDogc3RyaW5nKTogTmV4dEFwaVJlc3BvbnNlPERhdGE+XG4gIHJlZGlyZWN0KHN0YXR1czogbnVtYmVyLCB1cmw6IHN0cmluZyk6IE5leHRBcGlSZXNwb25zZTxEYXRhPlxuXG4gIC8qKlxuICAgKiBTZXQgZHJhZnQgbW9kZVxuICAgKi9cbiAgc2V0RHJhZnRNb2RlOiAob3B0aW9uczogeyBlbmFibGU6IGJvb2xlYW4gfSkgPT4gTmV4dEFwaVJlc3BvbnNlPERhdGE+XG5cbiAgLyoqXG4gICAqIFNldCBwcmV2aWV3IGRhdGEgZm9yIE5leHQuanMnIHByZXJlbmRlciBtb2RlXG4gICAqL1xuICBzZXRQcmV2aWV3RGF0YTogKFxuICAgIGRhdGE6IG9iamVjdCB8IHN0cmluZyxcbiAgICBvcHRpb25zPzoge1xuICAgICAgLyoqXG4gICAgICAgKiBTcGVjaWZpZXMgdGhlIG51bWJlciAoaW4gc2Vjb25kcykgZm9yIHRoZSBwcmV2aWV3IHNlc3Npb24gdG8gbGFzdCBmb3IuXG4gICAgICAgKiBUaGUgZ2l2ZW4gbnVtYmVyIHdpbGwgYmUgY29udmVydGVkIHRvIGFuIGludGVnZXIgYnkgcm91bmRpbmcgZG93bi5cbiAgICAgICAqIEJ5IGRlZmF1bHQsIG5vIG1heGltdW0gYWdlIGlzIHNldCBhbmQgdGhlIHByZXZpZXcgc2Vzc2lvbiBmaW5pc2hlc1xuICAgICAgICogd2hlbiB0aGUgY2xpZW50IHNodXRzIGRvd24gKGJyb3dzZXIgaXMgY2xvc2VkKS5cbiAgICAgICAqL1xuICAgICAgbWF4QWdlPzogbnVtYmVyXG4gICAgICAvKipcbiAgICAgICAqIFNwZWNpZmllcyB0aGUgcGF0aCBmb3IgdGhlIHByZXZpZXcgc2Vzc2lvbiB0byB3b3JrIHVuZGVyLiBCeSBkZWZhdWx0LFxuICAgICAgICogdGhlIHBhdGggaXMgY29uc2lkZXJlZCB0aGUgXCJkZWZhdWx0IHBhdGhcIiwgaS5lLiwgYW55IHBhZ2VzIHVuZGVyIFwiL1wiLlxuICAgICAgICovXG4gICAgICBwYXRoPzogc3RyaW5nXG4gICAgfVxuICApID0+IE5leHRBcGlSZXNwb25zZTxEYXRhPlxuXG4gIC8qKlxuICAgKiBDbGVhciBwcmV2aWV3IGRhdGEgZm9yIE5leHQuanMnIHByZXJlbmRlciBtb2RlXG4gICAqL1xuICBjbGVhclByZXZpZXdEYXRhOiAob3B0aW9ucz86IHsgcGF0aD86IHN0cmluZyB9KSA9PiBOZXh0QXBpUmVzcG9uc2U8RGF0YT5cblxuICAvKipcbiAgICogUmV2YWxpZGF0ZSBhIHNwZWNpZmljIHBhZ2UgYW5kIHJlZ2VuZXJhdGUgaXQgdXNpbmcgT24tRGVtYW5kIEluY3JlbWVudGFsXG4gICAqIFN0YXRpYyBSZWdlbmVyYXRpb24uXG4gICAqIFRoZSBwYXRoIHNob3VsZCBiZSBhbiBhY3R1YWwgcGF0aCwgbm90IGEgcmV3cml0dGVuIHBhdGguIEUuZy4gZm9yXG4gICAqIFwiL2Jsb2cvW3NsdWddXCIgdGhpcyBzaG91bGQgYmUgXCIvYmxvZy9wb3N0LTFcIi5cbiAgICogQGxpbmsgaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2J1aWxkaW5nLXlvdXItYXBwbGljYXRpb24vZGF0YS1mZXRjaGluZy9pbmNyZW1lbnRhbC1zdGF0aWMtcmVnZW5lcmF0aW9uI29uLWRlbWFuZC1yZXZhbGlkYXRpb24td2l0aC1yZXZhbGlkYXRlcGF0aFxuICAgKi9cbiAgcmV2YWxpZGF0ZTogKFxuICAgIHVybFBhdGg6IHN0cmluZyxcbiAgICBvcHRzPzoge1xuICAgICAgdW5zdGFibGVfb25seUdlbmVyYXRlZD86IGJvb2xlYW5cbiAgICB9XG4gICkgPT4gUHJvbWlzZTx2b2lkPlxufVxuXG4vKipcbiAqIE5leHQgYEFQSWAgcm91dGUgaGFuZGxlclxuICovXG5leHBvcnQgdHlwZSBOZXh0QXBpSGFuZGxlcjxUID0gYW55PiA9IChcbiAgcmVxOiBOZXh0QXBpUmVxdWVzdCxcbiAgcmVzOiBOZXh0QXBpUmVzcG9uc2U8VD5cbikgPT4gdW5rbm93biB8IFByb21pc2U8dW5rbm93bj5cblxuLyoqXG4gKiBVdGlsc1xuICovXG5leHBvcnQgZnVuY3Rpb24gZXhlY09uY2U8VCBleHRlbmRzICguLi5hcmdzOiBhbnlbXSkgPT4gUmV0dXJuVHlwZTxUPj4oXG4gIGZuOiBUXG4pOiBUIHtcbiAgbGV0IHVzZWQgPSBmYWxzZVxuICBsZXQgcmVzdWx0OiBSZXR1cm5UeXBlPFQ+XG5cbiAgcmV0dXJuICgoLi4uYXJnczogYW55W10pID0+IHtcbiAgICBpZiAoIXVzZWQpIHtcbiAgICAgIHVzZWQgPSB0cnVlXG4gICAgICByZXN1bHQgPSBmbiguLi5hcmdzKVxuICAgIH1cbiAgICByZXR1cm4gcmVzdWx0XG4gIH0pIGFzIFRcbn1cblxuLy8gU2NoZW1lOiBodHRwczovL3Rvb2xzLmlldGYub3JnL2h0bWwvcmZjMzk4NiNzZWN0aW9uLTMuMVxuLy8gQWJzb2x1dGUgVVJMOiBodHRwczovL3Rvb2xzLmlldGYub3JnL2h0bWwvcmZjMzk4NiNzZWN0aW9uLTQuM1xuY29uc3QgQUJTT0xVVEVfVVJMX1JFR0VYID0gL15bYS16QS1aXVthLXpBLVpcXGQrXFwtLl0qPzovXG5leHBvcnQgY29uc3QgaXNBYnNvbHV0ZVVybCA9ICh1cmw6IHN0cmluZykgPT4gQUJTT0xVVEVfVVJMX1JFR0VYLnRlc3QodXJsKVxuXG5leHBvcnQgZnVuY3Rpb24gZ2V0TG9jYXRpb25PcmlnaW4oKSB7XG4gIGNvbnN0IHsgcHJvdG9jb2wsIGhvc3RuYW1lLCBwb3J0IH0gPSB3aW5kb3cubG9jYXRpb25cbiAgcmV0dXJuIGAke3Byb3RvY29sfS8vJHtob3N0bmFtZX0ke3BvcnQgPyAnOicgKyBwb3J0IDogJyd9YFxufVxuXG5leHBvcnQgZnVuY3Rpb24gZ2V0VVJMKCkge1xuICBjb25zdCB7IGhyZWYgfSA9IHdpbmRvdy5sb2NhdGlvblxuICBjb25zdCBvcmlnaW4gPSBnZXRMb2NhdGlvbk9yaWdpbigpXG4gIHJldHVybiBocmVmLnN1YnN0cmluZyhvcmlnaW4ubGVuZ3RoKVxufVxuXG5leHBvcnQgZnVuY3Rpb24gZ2V0RGlzcGxheU5hbWU8UD4oQ29tcG9uZW50OiBDb21wb25lbnRUeXBlPFA+KSB7XG4gIHJldHVybiB0eXBlb2YgQ29tcG9uZW50ID09PSAnc3RyaW5nJ1xuICAgID8gQ29tcG9uZW50XG4gICAgOiBDb21wb25lbnQuZGlzcGxheU5hbWUgfHwgQ29tcG9uZW50Lm5hbWUgfHwgJ1Vua25vd24nXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBpc1Jlc1NlbnQocmVzOiBTZXJ2ZXJSZXNwb25zZSkge1xuICByZXR1cm4gcmVzLmZpbmlzaGVkIHx8IHJlcy5oZWFkZXJzU2VudFxufVxuXG5leHBvcnQgZnVuY3Rpb24gbm9ybWFsaXplUmVwZWF0ZWRTbGFzaGVzKHVybDogc3RyaW5nKSB7XG4gIGNvbnN0IHVybFBhcnRzID0gdXJsLnNwbGl0KCc/JylcbiAgY29uc3QgdXJsTm9RdWVyeSA9IHVybFBhcnRzWzBdXG5cbiAgcmV0dXJuIChcbiAgICB1cmxOb1F1ZXJ5XG4gICAgICAvLyBmaXJzdCB3ZSByZXBsYWNlIGFueSBub24tZW5jb2RlZCBiYWNrc2xhc2hlcyB3aXRoIGZvcndhcmRcbiAgICAgIC8vIHRoZW4gbm9ybWFsaXplIHJlcGVhdGVkIGZvcndhcmQgc2xhc2hlc1xuICAgICAgLnJlcGxhY2UoL1xcXFwvZywgJy8nKVxuICAgICAgLnJlcGxhY2UoL1xcL1xcLysvZywgJy8nKSArXG4gICAgKHVybFBhcnRzWzFdID8gYD8ke3VybFBhcnRzLnNsaWNlKDEpLmpvaW4oJz8nKX1gIDogJycpXG4gIClcbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGxvYWRHZXRJbml0aWFsUHJvcHM8XG4gIEMgZXh0ZW5kcyBCYXNlQ29udGV4dCxcbiAgSVAgPSB7fSxcbiAgUCA9IHt9LFxuPihBcHA6IE5leHRDb21wb25lbnRUeXBlPEMsIElQLCBQPiwgY3R4OiBDKTogUHJvbWlzZTxJUD4ge1xuICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICAgIGlmIChBcHAucHJvdG90eXBlPy5nZXRJbml0aWFsUHJvcHMpIHtcbiAgICAgIGNvbnN0IG1lc3NhZ2UgPSBgXCIke2dldERpc3BsYXlOYW1lKFxuICAgICAgICBBcHBcbiAgICAgICl9LmdldEluaXRpYWxQcm9wcygpXCIgaXMgZGVmaW5lZCBhcyBhbiBpbnN0YW5jZSBtZXRob2QgLSB2aXNpdCBodHRwczovL25leHRqcy5vcmcvZG9jcy9tZXNzYWdlcy9nZXQtaW5pdGlhbC1wcm9wcy1hcy1hbi1pbnN0YW5jZS1tZXRob2QgZm9yIG1vcmUgaW5mb3JtYXRpb24uYFxuICAgICAgdGhyb3cgbmV3IEVycm9yKG1lc3NhZ2UpXG4gICAgfVxuICB9XG4gIC8vIHdoZW4gY2FsbGVkIGZyb20gX2FwcCBgY3R4YCBpcyBuZXN0ZWQgaW4gYGN0eGBcbiAgY29uc3QgcmVzID0gY3R4LnJlcyB8fCAoY3R4LmN0eCAmJiBjdHguY3R4LnJlcylcblxuICBpZiAoIUFwcC5nZXRJbml0aWFsUHJvcHMpIHtcbiAgICBpZiAoY3R4LmN0eCAmJiBjdHguQ29tcG9uZW50KSB7XG4gICAgICAvLyBAdHMtaWdub3JlIHBhZ2VQcm9wcyBkZWZhdWx0XG4gICAgICByZXR1cm4ge1xuICAgICAgICBwYWdlUHJvcHM6IGF3YWl0IGxvYWRHZXRJbml0aWFsUHJvcHMoY3R4LkNvbXBvbmVudCwgY3R4LmN0eCksXG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiB7fSBhcyBJUFxuICB9XG5cbiAgY29uc3QgcHJvcHMgPSBhd2FpdCBBcHAuZ2V0SW5pdGlhbFByb3BzKGN0eClcblxuICBpZiAocmVzICYmIGlzUmVzU2VudChyZXMpKSB7XG4gICAgcmV0dXJuIHByb3BzXG4gIH1cblxuICBpZiAoIXByb3BzKSB7XG4gICAgY29uc3QgbWVzc2FnZSA9IGBcIiR7Z2V0RGlzcGxheU5hbWUoXG4gICAgICBBcHBcbiAgICApfS5nZXRJbml0aWFsUHJvcHMoKVwiIHNob3VsZCByZXNvbHZlIHRvIGFuIG9iamVjdC4gQnV0IGZvdW5kIFwiJHtwcm9wc31cIiBpbnN0ZWFkLmBcbiAgICB0aHJvdyBuZXcgRXJyb3IobWVzc2FnZSlcbiAgfVxuXG4gIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gICAgaWYgKE9iamVjdC5rZXlzKHByb3BzKS5sZW5ndGggPT09IDAgJiYgIWN0eC5jdHgpIHtcbiAgICAgIGNvbnNvbGUud2FybihcbiAgICAgICAgYCR7Z2V0RGlzcGxheU5hbWUoXG4gICAgICAgICAgQXBwXG4gICAgICAgICl9IHJldHVybmVkIGFuIGVtcHR5IG9iamVjdCBmcm9tIFxcYGdldEluaXRpYWxQcm9wc1xcYC4gVGhpcyBkZS1vcHRpbWl6ZXMgYW5kIHByZXZlbnRzIGF1dG9tYXRpYyBzdGF0aWMgb3B0aW1pemF0aW9uLiBodHRwczovL25leHRqcy5vcmcvZG9jcy9tZXNzYWdlcy9lbXB0eS1vYmplY3QtZ2V0SW5pdGlhbFByb3BzYFxuICAgICAgKVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiBwcm9wc1xufVxuXG5leHBvcnQgY29uc3QgU1AgPSB0eXBlb2YgcGVyZm9ybWFuY2UgIT09ICd1bmRlZmluZWQnXG5leHBvcnQgY29uc3QgU1QgPVxuICBTUCAmJlxuICAoWydtYXJrJywgJ21lYXN1cmUnLCAnZ2V0RW50cmllc0J5TmFtZSddIGFzIGNvbnN0KS5ldmVyeShcbiAgICAobWV0aG9kKSA9PiB0eXBlb2YgcGVyZm9ybWFuY2VbbWV0aG9kXSA9PT0gJ2Z1bmN0aW9uJ1xuICApXG5cbmV4cG9ydCBjbGFzcyBEZWNvZGVFcnJvciBleHRlbmRzIEVycm9yIHt9XG5leHBvcnQgY2xhc3MgTm9ybWFsaXplRXJyb3IgZXh0ZW5kcyBFcnJvciB7fVxuZXhwb3J0IGNsYXNzIFBhZ2VOb3RGb3VuZEVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICBjb2RlOiBzdHJpbmdcblxuICBjb25zdHJ1Y3RvcihwYWdlOiBzdHJpbmcpIHtcbiAgICBzdXBlcigpXG4gICAgdGhpcy5jb2RlID0gJ0VOT0VOVCdcbiAgICB0aGlzLm5hbWUgPSAnUGFnZU5vdEZvdW5kRXJyb3InXG4gICAgdGhpcy5tZXNzYWdlID0gYENhbm5vdCBmaW5kIG1vZHVsZSBmb3IgcGFnZTogJHtwYWdlfWBcbiAgfVxufVxuXG5leHBvcnQgY2xhc3MgTWlzc2luZ1N0YXRpY1BhZ2UgZXh0ZW5kcyBFcnJvciB7XG4gIGNvbnN0cnVjdG9yKHBhZ2U6IHN0cmluZywgbWVzc2FnZTogc3RyaW5nKSB7XG4gICAgc3VwZXIoKVxuICAgIHRoaXMubWVzc2FnZSA9IGBGYWlsZWQgdG8gbG9hZCBzdGF0aWMgZmlsZSBmb3IgcGFnZTogJHtwYWdlfSAke21lc3NhZ2V9YFxuICB9XG59XG5cbmV4cG9ydCBjbGFzcyBNaWRkbGV3YXJlTm90Rm91bmRFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgY29kZTogc3RyaW5nXG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHN1cGVyKClcbiAgICB0aGlzLmNvZGUgPSAnRU5PRU5UJ1xuICAgIHRoaXMubWVzc2FnZSA9IGBDYW5ub3QgZmluZCB0aGUgbWlkZGxld2FyZSBtb2R1bGVgXG4gIH1cbn1cblxuZXhwb3J0IGludGVyZmFjZSBDYWNoZUZzIHtcbiAgZXhpc3RzU3luYzogdHlwZW9mIGZzLmV4aXN0c1N5bmNcbiAgcmVhZEZpbGU6IHR5cGVvZiBmcy5wcm9taXNlcy5yZWFkRmlsZVxuICByZWFkRmlsZVN5bmM6IHR5cGVvZiBmcy5yZWFkRmlsZVN5bmNcbiAgd3JpdGVGaWxlKGY6IHN0cmluZywgZDogYW55KTogUHJvbWlzZTx2b2lkPlxuICBta2RpcihkaXI6IHN0cmluZyk6IFByb21pc2U8dm9pZCB8IHN0cmluZz5cbiAgc3RhdChmOiBzdHJpbmcpOiBQcm9taXNlPHsgbXRpbWU6IERhdGUgfT5cbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHN0cmluZ2lmeUVycm9yKGVycm9yOiBFcnJvcikge1xuICByZXR1cm4gSlNPTi5zdHJpbmdpZnkoeyBtZXNzYWdlOiBlcnJvci5tZXNzYWdlLCBzdGFjazogZXJyb3Iuc3RhY2sgfSlcbn1cbiJdLCJuYW1lcyI6WyJEZWNvZGVFcnJvciIsIk1pZGRsZXdhcmVOb3RGb3VuZEVycm9yIiwiTWlzc2luZ1N0YXRpY1BhZ2UiLCJOb3JtYWxpemVFcnJvciIsIlBhZ2VOb3RGb3VuZEVycm9yIiwiU1AiLCJTVCIsIldFQl9WSVRBTFMiLCJleGVjT25jZSIsImdldERpc3BsYXlOYW1lIiwiZ2V0TG9jYXRpb25PcmlnaW4iLCJnZXRVUkwiLCJpc0Fic29sdXRlVXJsIiwiaXNSZXNTZW50IiwibG9hZEdldEluaXRpYWxQcm9wcyIsIm5vcm1hbGl6ZVJlcGVhdGVkU2xhc2hlcyIsInN0cmluZ2lmeUVycm9yIiwiZm4iLCJ1c2VkIiwicmVzdWx0IiwiYXJncyIsIkFCU09MVVRFX1VSTF9SRUdFWCIsInVybCIsInRlc3QiLCJwcm90b2NvbCIsImhvc3RuYW1lIiwicG9ydCIsIndpbmRvdyIsImxvY2F0aW9uIiwiaHJlZiIsIm9yaWdpbiIsInN1YnN0cmluZyIsImxlbmd0aCIsIkNvbXBvbmVudCIsImRpc3BsYXlOYW1lIiwibmFtZSIsInJlcyIsImZpbmlzaGVkIiwiaGVhZGVyc1NlbnQiLCJ1cmxQYXJ0cyIsInNwbGl0IiwidXJsTm9RdWVyeSIsInJlcGxhY2UiLCJzbGljZSIsImpvaW4iLCJBcHAiLCJjdHgiLCJwcm9jZXNzIiwiZW52IiwiTk9ERV9FTlYiLCJwcm90b3R5cGUiLCJnZXRJbml0aWFsUHJvcHMiLCJtZXNzYWdlIiwiRXJyb3IiLCJwYWdlUHJvcHMiLCJwcm9wcyIsIk9iamVjdCIsImtleXMiLCJjb25zb2xlIiwid2FybiIsInBlcmZvcm1hbmNlIiwiZXZlcnkiLCJtZXRob2QiLCJjb25zdHJ1Y3RvciIsInBhZ2UiLCJjb2RlIiwiZXJyb3IiLCJKU09OIiwic3RyaW5naWZ5Iiwic3RhY2siXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useAddresses.ts":
/*!***********************************!*\
  !*** ./src/hooks/useAddresses.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addressKeys: () => (/* binding */ addressKeys),\n/* harmony export */   useAddressCacheUtils: () => (/* binding */ useAddressCacheUtils),\n/* harmony export */   useAddresses: () => (/* binding */ useAddresses),\n/* harmony export */   useAddressesAuthSync: () => (/* binding */ useAddressesAuthSync),\n/* harmony export */   useCreateAddress: () => (/* binding */ useCreateAddress),\n/* harmony export */   useDeleteAddress: () => (/* binding */ useDeleteAddress),\n/* harmony export */   useRefreshAddresses: () => (/* binding */ useRefreshAddresses),\n/* harmony export */   useSetDefaultAddress: () => (/* binding */ useSetDefaultAddress)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _stores_authStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/stores/authStore */ \"(app-pages-browser)/./src/stores/authStore.ts\");\n\n\n\n\n// 🏭 Query Key Factory - Organized and type-safe\nconst addressKeys = {\n    all: [\n        'addresses'\n    ],\n    lists: ()=>[\n            ...addressKeys.all,\n            'list'\n        ],\n    list: (userId)=>[\n            ...addressKeys.lists(),\n            {\n                userId\n            }\n        ],\n    detail: (id)=>[\n            ...addressKeys.all,\n            'detail',\n            id\n        ],\n    // Future endpoints için hazır\n    favorites: ()=>[\n            ...addressKeys.all,\n            'favorites'\n        ],\n    search: (query)=>[\n            ...addressKeys.all,\n            'search',\n            {\n                query\n            }\n        ]\n};\n// 🔄 Auth State Sync Helper - Login sonrası state senkronizasyonu için\nconst useAddressesAuthSync = ()=>{\n    // 🎯 CRITICAL FIX: Auth store'dan direkt bilgi al - TanStack Query race condition'ını bypas et\n    const authStore = (0,_stores_authStore__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    const { data: userData, isLoading: userLoading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.useUserInfo)();\n    // 🔄 IMPROVED: Auth store priority - race condition'ı önle\n    const user = authStore.user || userData;\n    // 🎯 Auth durumunu kontrol et - auth store'dan authenticated ise hazır sayılır\n    const isAuthReady = authStore.isAuthenticated && !authStore.isLoading && !!(user === null || user === void 0 ? void 0 : user.id);\n    if (true) {\n        console.log('🔄 Auth sync status:', {\n            isAuthenticated: authStore.isAuthenticated,\n            authLoading: authStore.isLoading,\n            userLoading,\n            hasUser: !!user,\n            userId: user === null || user === void 0 ? void 0 : user.id,\n            isAuthReady,\n            sourceUser: authStore.user ? 'authStore' : 'tanstackQuery'\n        });\n    }\n    return {\n        isAuthReady,\n        user\n    };\n};\n// 📡 GET - Adres listesi (Optimized)\nconst useAddresses = ()=>{\n    // 🚀 CRITICAL FIX: Auth state senkronizasyonu için helper kullan\n    const { isAuthReady, user } = useAddressesAuthSync();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    // 🔍 Enhanced debug info (only in development)\n    if (true) {\n        console.log('🔍 useAddresses hook status:', {\n            hasUser: !!user,\n            userId: user === null || user === void 0 ? void 0 : user.id,\n            isAuthReady\n        });\n    }\n    // TanStack Query otomatik olarak user ID'ye göre cache'i yönetiyor\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)({\n        queryKey: (user === null || user === void 0 ? void 0 : user.id) ? addressKeys.list(user.id) : [\n            'addresses',\n            'no-user'\n        ],\n        queryFn: {\n            \"useAddresses.useQuery\": async ()=>{\n                if (!(user === null || user === void 0 ? void 0 : user.id)) {\n                    console.log('⚠️ useAddresses: No user ID available, returning empty array');\n                    return [];\n                }\n                console.log('📍 Fetching addresses for user:', user.id);\n                const result = await _services_api__WEBPACK_IMPORTED_MODULE_0__.addressService.getAddresses();\n                if (!result.success) {\n                    const error = new Error(result.error || 'Adresler yüklenemedi');\n                    error.name = 'AddressLoadError';\n                    throw error;\n                }\n                // ✅ ID'ye göre sıralama (küçük ID'ler önce - eklenme sırasına göre)\n                const addresses = result.data || [];\n                const sortedAddresses = addresses.sort({\n                    \"useAddresses.useQuery.sortedAddresses\": (a, b)=>{\n                        // 🏠 1. ÖNCE: Varsayılan adres kontrolü\n                        if (a.isDefault && !b.isDefault) return -1; // a varsayılan ise önce\n                        if (!a.isDefault && b.isDefault) return 1; // b varsayılan ise önce\n                        // 📅 2. SONRA: ID'ye göre sıralama (ekleme sırasına göre)\n                        if (!a.id && !b.id) return 0;\n                        if (!a.id) return 1;\n                        if (!b.id) return -1;\n                        return a.id - b.id; // Ascending sort (küçük ID'ler önce)\n                    }\n                }[\"useAddresses.useQuery.sortedAddresses\"]);\n                console.log('📍 Addresses sorted (default first, then by ID):', sortedAddresses.map({\n                    \"useAddresses.useQuery\": (addr)=>({\n                            id: addr.id,\n                            title: addr.title,\n                            isDefault: addr.isDefault\n                        })\n                }[\"useAddresses.useQuery\"]));\n                return sortedAddresses;\n            }\n        }[\"useAddresses.useQuery\"],\n        enabled: isAuthReady,\n        staleTime: 5 * 60 * 1000,\n        gcTime: 10 * 60 * 1000,\n        refetchOnMount: true,\n        refetchOnWindowFocus: false,\n        refetchOnReconnect: true,\n        retry: {\n            \"useAddresses.useQuery\": (failureCount, error)=>{\n                // Smart retry logic\n                if (error.name === 'AddressLoadError') {\n                    return failureCount < 2; // Max 2 retry for API errors\n                }\n                return failureCount < 3; // Max 3 retry for other errors\n            }\n        }[\"useAddresses.useQuery\"],\n        retryDelay: {\n            \"useAddresses.useQuery\": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)\n        }[\"useAddresses.useQuery\"]\n    });\n};\n// ➕ POST - Adres ekleme (Optimized)\nconst useCreateAddress = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    // 🎯 CRITICAL FIX: Auth store'dan direkt bilgi al\n    const authStore = (0,_stores_authStore__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    const { data: userData } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.useUserInfo)();\n    // 🔄 IMPROVED: Auth store priority - race condition'ı önle\n    const user = authStore.user || userData;\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({\n        mutationFn: {\n            \"useCreateAddress.useMutation\": async (addressData)=>{\n                console.log('🔄 TanStack Query: Creating address...', addressData);\n                // 🔐 Auth check - prevent creating address for unauthenticated users\n                if (!authStore.isAuthenticated || !(user === null || user === void 0 ? void 0 : user.id)) {\n                    console.error('❌ Authentication Error:', {\n                        isAuthenticated: authStore.isAuthenticated,\n                        hasUser: !!user,\n                        userId: user === null || user === void 0 ? void 0 : user.id\n                    });\n                    const error = new Error('Kullanıcı oturum açmamış veya kullanıcı bilgileri yüklenmemiş');\n                    error.name = 'AuthenticationError';\n                    throw error;\n                }\n                // 🏠 İlk adres kontrolü - mevcut adresleri al\n                const currentAddresses = queryClient.getQueryData(addressKeys.list(user.id)) || [];\n                // ✅ Eğer hiç adres yoksa, bu adres varsayılan olarak ayarlanır\n                const isFirstAddress = currentAddresses.length === 0;\n                const finalAddressData = {\n                    ...addressData,\n                    isDefault: isFirstAddress ? true : addressData.isDefault\n                };\n                if (isFirstAddress) {\n                    console.log('🏠 İlk adres ekleniyor - otomatik varsayılan olarak ayarlandı');\n                }\n                const result = await _services_api__WEBPACK_IMPORTED_MODULE_0__.addressService.createAddress(finalAddressData);\n                if (!result.success) {\n                    const error = new Error(result.error || 'Adres eklenemedi');\n                    error.name = 'AddressCreateError';\n                    throw error;\n                }\n                console.log('✅ TanStack Query: Address created successfully');\n                return result.data;\n            }\n        }[\"useCreateAddress.useMutation\"],\n        // 🚀 Optimistic Update\n        onMutate: {\n            \"useCreateAddress.useMutation\": async (newAddressData)=>{\n                const queryKey = addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0);\n                // Cancel any outgoing refetches\n                await queryClient.cancelQueries({\n                    queryKey\n                });\n                // Snapshot the previous value for rollback\n                const previousAddresses = queryClient.getQueryData(queryKey);\n                // Optimistically update to the new value\n                if (previousAddresses) {\n                    // 🏠 İlk adres kontrolü (optimistic update için)\n                    const isFirstAddress = previousAddresses.length === 0;\n                    const optimisticAddress = {\n                        id: Date.now(),\n                        title: newAddressData.title,\n                        fullAddress: newAddressData.fullAddress,\n                        city: newAddressData.city,\n                        district: newAddressData.district,\n                        postalCode: newAddressData.postalCode,\n                        isDefault: isFirstAddress ? true : newAddressData.isDefault\n                    };\n                    if (isFirstAddress) {\n                        console.log('🏠 Optimistic: İlk adres - otomatik varsayılan olarak ayarlandı');\n                    }\n                    // ✅ Doğru sıralama algoritması ile optimistic ekle\n                    const updatedAddresses = [\n                        ...previousAddresses,\n                        optimisticAddress\n                    ];\n                    const sortedAddresses = updatedAddresses.sort({\n                        \"useCreateAddress.useMutation.sortedAddresses\": (a, b)=>{\n                            // 🏠 1. ÖNCE: Varsayılan adres kontrolü\n                            if (a.isDefault && !b.isDefault) return -1; // a varsayılan ise önce\n                            if (!a.isDefault && b.isDefault) return 1; // b varsayılan ise önce\n                            // 📅 2. SONRA: ID'ye göre sıralama (ekleme sırasına göre)\n                            if (!a.id && !b.id) return 0;\n                            if (!a.id) return 1;\n                            if (!b.id) return -1;\n                            return a.id - b.id; // Ascending sort\n                        }\n                    }[\"useCreateAddress.useMutation.sortedAddresses\"]);\n                    queryClient.setQueryData(queryKey, sortedAddresses);\n                    console.log('🚀 Optimistic update: Address added to cache with smart sorting (default first)');\n                }\n                // Return context for rollback\n                return {\n                    previousAddresses\n                };\n            }\n        }[\"useCreateAddress.useMutation\"],\n        onSuccess: {\n            \"useCreateAddress.useMutation\": (newAddress, variables, context)=>{\n                console.log('✅ TanStack Query: Address creation confirmed by server');\n                // Update cache with real server data\n                const queryKey = addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0);\n                queryClient.setQueryData(queryKey, {\n                    \"useCreateAddress.useMutation\": function() {\n                        let oldAddresses = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];\n                        // Remove optimistic entry and add real one\n                        const withoutOptimistic = oldAddresses.filter({\n                            \"useCreateAddress.useMutation.withoutOptimistic\": (addr)=>typeof addr.id === 'number' && addr.id > Date.now() - 10000 ? false : true\n                        }[\"useCreateAddress.useMutation.withoutOptimistic\"]);\n                        // ✅ Doğru sıralama algoritması ile ekle\n                        const updatedAddresses = [\n                            ...withoutOptimistic,\n                            newAddress\n                        ];\n                        const sortedAddresses = updatedAddresses.sort({\n                            \"useCreateAddress.useMutation.sortedAddresses\": (a, b)=>{\n                                // 🏠 1. ÖNCE: Varsayılan adres kontrolü\n                                if (a.isDefault && !b.isDefault) return -1; // a varsayılan ise önce\n                                if (!a.isDefault && b.isDefault) return 1; // b varsayılan ise önce\n                                // 📅 2. SONRA: ID'ye göre sıralama (ekleme sırasına göre)\n                                if (!a.id && !b.id) return 0;\n                                if (!a.id) return 1;\n                                if (!b.id) return -1;\n                                return a.id - b.id; // Ascending sort\n                            }\n                        }[\"useCreateAddress.useMutation.sortedAddresses\"]);\n                        console.log('📍 Cache updated with smart sorted addresses:', sortedAddresses.map({\n                            \"useCreateAddress.useMutation\": (addr)=>({\n                                    id: addr.id,\n                                    title: addr.title,\n                                    isDefault: addr.isDefault\n                                })\n                        }[\"useCreateAddress.useMutation\"]));\n                        return sortedAddresses;\n                    }\n                }[\"useCreateAddress.useMutation\"]);\n            }\n        }[\"useCreateAddress.useMutation\"],\n        onError: {\n            \"useCreateAddress.useMutation\": (error, variables, context)=>{\n                console.error('❌ TanStack Query: Address creation failed:', error);\n                // Rollback optimistic update\n                if (context === null || context === void 0 ? void 0 : context.previousAddresses) {\n                    const queryKey = addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0);\n                    queryClient.setQueryData(queryKey, context.previousAddresses);\n                    console.log('🔄 Rollback: Optimistic update reverted');\n                }\n            }\n        }[\"useCreateAddress.useMutation\"],\n        onSettled: {\n            \"useCreateAddress.useMutation\": ()=>{\n                // Always refetch to ensure consistency\n                const queryKey = addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0);\n                queryClient.invalidateQueries({\n                    queryKey\n                });\n            }\n        }[\"useCreateAddress.useMutation\"]\n    });\n};\n// 🗑️ DELETE - Adres silme (Optimized)\nconst useDeleteAddress = (onDefaultRemoved)=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const { data: userData } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.useUserInfo)();\n    const user = userData;\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({\n        mutationFn: {\n            \"useDeleteAddress.useMutation\": async (addressId)=>{\n                console.log('🔄 TanStack Query: Deleting address:', addressId);\n                const result = await _services_api__WEBPACK_IMPORTED_MODULE_0__.addressService.deleteAddress(addressId, (user === null || user === void 0 ? void 0 : user.id) || 0);\n                if (!result.success) {\n                    const error = new Error(result.error || 'Adres silinemedi');\n                    error.name = 'AddressDeleteError';\n                    throw error;\n                }\n                console.log('✅ TanStack Query: Address deleted successfully');\n                return addressId;\n            }\n        }[\"useDeleteAddress.useMutation\"],\n        // 🚀 Optimistic Update\n        onMutate: {\n            \"useDeleteAddress.useMutation\": async (addressId)=>{\n                const queryKey = addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0);\n                // Cancel any outgoing refetches\n                await queryClient.cancelQueries({\n                    queryKey\n                });\n                // Snapshot the previous value\n                const previousAddresses = queryClient.getQueryData(queryKey);\n                // Find removed address info\n                const removedAddress = previousAddresses === null || previousAddresses === void 0 ? void 0 : previousAddresses.find({\n                    \"useDeleteAddress.useMutation\": (addr)=>addr.id === addressId\n                }[\"useDeleteAddress.useMutation\"]);\n                // 🏠 Check if removing default address\n                const isRemovingDefault = removedAddress === null || removedAddress === void 0 ? void 0 : removedAddress.isDefault;\n                const remainingAddresses = (previousAddresses === null || previousAddresses === void 0 ? void 0 : previousAddresses.filter({\n                    \"useDeleteAddress.useMutation\": (addr)=>addr.id !== addressId\n                }[\"useDeleteAddress.useMutation\"])) || [];\n                // Optimistically remove from cache\n                if (previousAddresses) {\n                    let optimisticAddresses = previousAddresses.filter({\n                        \"useDeleteAddress.useMutation.optimisticAddresses\": (addr)=>addr.id !== addressId\n                    }[\"useDeleteAddress.useMutation.optimisticAddresses\"]);\n                    // 🚨 If we're removing the default address and there are other addresses\n                    if (isRemovingDefault && optimisticAddresses.length > 0) {\n                        // Make the first remaining address default (by ID order)\n                        const nextDefaultAddress = optimisticAddresses.sort({\n                            \"useDeleteAddress.useMutation\": (a, b)=>(a.id || 0) - (b.id || 0)\n                        }[\"useDeleteAddress.useMutation\"])[0];\n                        optimisticAddresses = optimisticAddresses.map({\n                            \"useDeleteAddress.useMutation\": (addr)=>({\n                                    ...addr,\n                                    isDefault: addr.id === nextDefaultAddress.id\n                                })\n                        }[\"useDeleteAddress.useMutation\"]);\n                        console.log('🏠 Default address removed, setting next address as default:', nextDefaultAddress.title);\n                    }\n                    // Apply sorting\n                    const sortedAddresses = optimisticAddresses.sort({\n                        \"useDeleteAddress.useMutation.sortedAddresses\": (a, b)=>{\n                            if (a.isDefault && !b.isDefault) return -1;\n                            if (!a.isDefault && b.isDefault) return 1;\n                            return (a.id || 0) - (b.id || 0);\n                        }\n                    }[\"useDeleteAddress.useMutation.sortedAddresses\"]);\n                    queryClient.setQueryData(queryKey, sortedAddresses);\n                    console.log('🚀 Optimistic update: Address removed from cache with auto-default handling');\n                }\n                return {\n                    previousAddresses,\n                    removedAddress,\n                    wasDefault: isRemovingDefault,\n                    remainingAddresses\n                };\n            }\n        }[\"useDeleteAddress.useMutation\"],\n        onSuccess: {\n            \"useDeleteAddress.useMutation\": (deletedAddressId, addressId, context)=>{\n                console.log('✅ TanStack Query: Address deletion confirmed by server');\n                // 🏠 Check if we removed a default address and need to set a new one\n                if ((context === null || context === void 0 ? void 0 : context.wasDefault) && (context === null || context === void 0 ? void 0 : context.remainingAddresses.length) > 0) {\n                    // Find the next default address (first by ID order)\n                    const nextDefaultAddress = context.remainingAddresses.sort({\n                        \"useDeleteAddress.useMutation\": (a, b)=>(a.id || 0) - (b.id || 0)\n                    }[\"useDeleteAddress.useMutation\"])[0];\n                    console.log('🏠 Default address was removed, next default should be:', nextDefaultAddress.title);\n                    // Call the callback if provided with both addresses\n                    if (onDefaultRemoved && nextDefaultAddress) {\n                        onDefaultRemoved(nextDefaultAddress, context.removedAddress);\n                    }\n                }\n            }\n        }[\"useDeleteAddress.useMutation\"],\n        onError: {\n            \"useDeleteAddress.useMutation\": (error, addressId, context)=>{\n                console.error('❌ TanStack Query: Address deletion failed:', error);\n                // Rollback optimistic update\n                if (context === null || context === void 0 ? void 0 : context.previousAddresses) {\n                    const queryKey = addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0);\n                    queryClient.setQueryData(queryKey, context.previousAddresses);\n                    console.log('🔄 Rollback: Deleted address restored to cache');\n                }\n            }\n        }[\"useDeleteAddress.useMutation\"],\n        onSettled: {\n            \"useDeleteAddress.useMutation\": ()=>{\n                // Always refetch to ensure consistency\n                const queryKey = addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0);\n                queryClient.invalidateQueries({\n                    queryKey\n                });\n            }\n        }[\"useDeleteAddress.useMutation\"]\n    });\n};\n// 🏠 SET DEFAULT - Adres varsayılan yapma (Optimized)\nconst useSetDefaultAddress = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const { data: userData } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.useUserInfo)();\n    const user = userData;\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({\n        mutationFn: {\n            \"useSetDefaultAddress.useMutation\": async (addressId)=>{\n                console.log('🔄 TanStack Query: Setting address as default:', addressId);\n                const result = await _services_api__WEBPACK_IMPORTED_MODULE_0__.addressService.setDefaultAddress(addressId);\n                if (!result.success) {\n                    const error = new Error(result.error || 'Varsayılan adres ayarlanamadı');\n                    error.name = 'SetDefaultAddressError';\n                    throw error;\n                }\n                console.log('✅ TanStack Query: Address set as default successfully');\n                return result.data; // Updated address with isDefault: true\n            }\n        }[\"useSetDefaultAddress.useMutation\"],\n        // 🚀 Optimistic Update\n        onMutate: {\n            \"useSetDefaultAddress.useMutation\": async (addressId)=>{\n                const queryKey = addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0);\n                // Cancel any outgoing refetches\n                await queryClient.cancelQueries({\n                    queryKey\n                });\n                // Snapshot the previous value\n                const previousAddresses = queryClient.getQueryData(queryKey);\n                // Optimistically update addresses\n                if (previousAddresses) {\n                    const optimisticAddresses = previousAddresses.map({\n                        \"useSetDefaultAddress.useMutation.optimisticAddresses\": (addr)=>({\n                                ...addr,\n                                isDefault: addr.id === addressId\n                            })\n                    }[\"useSetDefaultAddress.useMutation.optimisticAddresses\"]);\n                    // ✅ Apply sorting to optimistic data\n                    const sortedAddresses = optimisticAddresses.sort({\n                        \"useSetDefaultAddress.useMutation.sortedAddresses\": (a, b)=>{\n                            // 🏠 1. ÖNCE: Varsayılan adres kontrolü\n                            if (a.isDefault && !b.isDefault) return -1; // a varsayılan ise önce\n                            if (!a.isDefault && b.isDefault) return 1; // b varsayılan ise önce\n                            // 📅 2. SONRA: ID'ye göre sıralama (ekleme sırasına göre)\n                            if (!a.id && !b.id) return 0;\n                            if (!a.id) return 1;\n                            if (!b.id) return -1;\n                            return a.id - b.id; // Ascending sort\n                        }\n                    }[\"useSetDefaultAddress.useMutation.sortedAddresses\"]);\n                    queryClient.setQueryData(queryKey, sortedAddresses);\n                    console.log('🚀 Optimistic update: Default address changed with smart sorting');\n                }\n                return {\n                    previousAddresses\n                };\n            }\n        }[\"useSetDefaultAddress.useMutation\"],\n        onSuccess: {\n            \"useSetDefaultAddress.useMutation\": (updatedAddress, addressId, context)=>{\n                console.log('✅ TanStack Query: Set default address confirmed by server');\n                // Update cache with real server data\n                const queryKey = addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0);\n                queryClient.setQueryData(queryKey, {\n                    \"useSetDefaultAddress.useMutation\": function() {\n                        let oldAddresses = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];\n                        const updatedAddresses = oldAddresses.map({\n                            \"useSetDefaultAddress.useMutation.updatedAddresses\": (addr)=>({\n                                    ...addr,\n                                    isDefault: addr.id === addressId\n                                })\n                        }[\"useSetDefaultAddress.useMutation.updatedAddresses\"]);\n                        // ✅ Apply sorting to server data\n                        const sortedAddresses = updatedAddresses.sort({\n                            \"useSetDefaultAddress.useMutation.sortedAddresses\": (a, b)=>{\n                                // 🏠 1. ÖNCE: Varsayılan adres kontrolü\n                                if (a.isDefault && !b.isDefault) return -1; // a varsayılan ise önce\n                                if (!a.isDefault && b.isDefault) return 1; // b varsayılan ise önce\n                                // 📅 2. SONRA: ID'ye göre sıralama (ekleme sırasına göre)\n                                if (!a.id && !b.id) return 0;\n                                if (!a.id) return 1;\n                                if (!b.id) return -1;\n                                return a.id - b.id; // Ascending sort\n                            }\n                        }[\"useSetDefaultAddress.useMutation.sortedAddresses\"]);\n                        console.log('📍 Cache updated with new default address (smart sorted):', sortedAddresses.map({\n                            \"useSetDefaultAddress.useMutation\": (addr)=>({\n                                    id: addr.id,\n                                    title: addr.title,\n                                    isDefault: addr.isDefault\n                                })\n                        }[\"useSetDefaultAddress.useMutation\"]));\n                        return sortedAddresses;\n                    }\n                }[\"useSetDefaultAddress.useMutation\"]);\n            }\n        }[\"useSetDefaultAddress.useMutation\"],\n        onError: {\n            \"useSetDefaultAddress.useMutation\": (error, addressId, context)=>{\n                console.error('❌ TanStack Query: Set default address failed:', error);\n                // Rollback optimistic update\n                if (context === null || context === void 0 ? void 0 : context.previousAddresses) {\n                    const queryKey = addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0);\n                    queryClient.setQueryData(queryKey, context.previousAddresses);\n                    console.log('🔄 Rollback: Default address change reverted');\n                }\n            }\n        }[\"useSetDefaultAddress.useMutation\"],\n        onSettled: {\n            \"useSetDefaultAddress.useMutation\": ()=>{\n                // Always refetch to ensure consistency\n                const queryKey = addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0);\n                queryClient.invalidateQueries({\n                    queryKey\n                });\n            }\n        }[\"useSetDefaultAddress.useMutation\"]\n    });\n};\n// 🔄 Manual refetch helper\nconst useRefreshAddresses = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const { data: userData } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.useUserInfo)();\n    const user = userData;\n    return ()=>{\n        queryClient.invalidateQueries({\n            queryKey: addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0)\n        });\n    };\n};\n// 📊 Cache utilities\nconst useAddressCacheUtils = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const { data: userData } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.useUserInfo)();\n    const user = userData;\n    return {\n        // Prefetch addresses\n        prefetchAddresses: ()=>{\n            return queryClient.prefetchQuery({\n                queryKey: addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0),\n                queryFn: async ()=>{\n                    const result = await _services_api__WEBPACK_IMPORTED_MODULE_0__.addressService.getAddresses();\n                    return result.success ? result.data || [] : [];\n                },\n                staleTime: 5 * 60 * 1000\n            });\n        },\n        // Get cached addresses without triggering fetch\n        getCachedAddresses: ()=>{\n            return queryClient.getQueryData(addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0));\n        },\n        // Set addresses manually (for testing or initial data)\n        setCachedAddresses: (addresses)=>{\n            queryClient.setQueryData(addressKeys.list((user === null || user === void 0 ? void 0 : user.id) || 0), addresses);\n        },\n        // Clear address cache\n        clearAddressCache: ()=>{\n            queryClient.removeQueries({\n                queryKey: addressKeys.all\n            });\n        }\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAddresses.ts\n"));

/***/ })

});